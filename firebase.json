{"functions": [{"source": "functions", "codebase": "default", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "openfit", "codebase": "openfit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "aifit", "codebase": "aifit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "agent_openfit", "codebase": "agent_openfit", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "firestore": {"database": "(default)", "location": "us-central1", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "flutter": {"platforms": {"android": {"default": {"projectId": "po2vf2ae7tal9invaj7jkf4a06hsac", "appId": "1:745521622245:android:eace8279975105f09c780c", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "po2vf2ae7tal9invaj7jkf4a06hsac", "appId": "1:745521622245:ios:4af840b9e0d1ed089c780c", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "po2vf2ae7tal9invaj7jkf4a06hsac", "configurations": {"android": "1:745521622245:android:eace8279975105f09c780c", "ios": "1:745521622245:ios:4af840b9e0d1ed089c780c"}}}}}}