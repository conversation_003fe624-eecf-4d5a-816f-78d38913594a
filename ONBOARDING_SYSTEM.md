# OpenFit Onboarding System

## Overview

I've created a comprehensive onboarding-based user collection system for your OpenFit app using your Firebase MCP integration. This system guides new users through a personalized setup process and stores their data in a structured way in Firestore.

## Architecture

### 🏗️ Core Components

1. **OnboardingService** (`lib/core/services/onboarding_service.dart`)
   - Handles all Firebase operations for onboarding
   - Creates initial user profiles
   - Updates onboarding progress
   - Completes onboarding process

2. **OnboardingProvider** (`lib/core/providers/onboarding_provider.dart`)
   - State management for onboarding flow
   - Manages onboarding data collection
   - Controls navigation between steps

3. **Onboarding Models**
   - `OnboardingStepModel` - Defines onboarding step structure
   - `OnboardingDataModel` - Collects user data during onboarding

4. **Onboarding Screens**
   - `OnboardingWrapper` - Main flow controller
   - `WelcomePage` - Introduction and app features
   - `PersonalInfoPage` - Basic user information
   - `FitnessGoalsPage` - Fitness goals and preferences
   - `PreferencesPage` - App settings and workout environments
   - `CompletionPage` - Onboarding completion summary

## 📊 Database Structure

### User Collection Schema

```json
{
  "users": {
    "{userId}": {
      "uid": "string",
      "email": "string", 
      "displayName": "string",
      "photoURL": "string?",
      
      // Onboarding Status
      "onboardingCompleted": "boolean",
      "onboardingCompletedAt": "timestamp?",
      "onboardingSteps": {
        "personalInfo": {
          "completed": "boolean",
          "completedAt": "timestamp",
          "data": "object"
        },
        "fitnessGoals": {
          "completed": "boolean", 
          "completedAt": "timestamp",
          "data": "object"
        },
        "preferences": {
          "completed": "boolean",
          "completedAt": "timestamp", 
          "data": "object"
        }
      },
      
      // User Profile Data
      "profile": {
        "dateOfBirth": "timestamp?",
        "gender": "string",
        "height": "number?", // in cm
        "weight": "number?", // in kg
        "preferredUnits": "string" // 'metric' | 'imperial'
      },
      
      // Fitness Information
      "fitness": {
        "fitnessLevel": "string", // 'beginner' | 'intermediate' | 'advanced'
        "fitnessGoals": ["string"],
        "targetMuscleGroups": ["string"],
        "activityLevel": "string",
        "availableEquipment": ["string"],
        "preferredWorkoutTypes": ["string"],
        "workoutDuration": "number", // in minutes
        "workoutsPerWeek": "number",
        "healthConditions": ["string"],
        "injuries": ["string"],
        "hasHealthRestrictions": "boolean"
      },
      
      // User Preferences
      "preferences": {
        "workoutsPerWeek": "number",
        "durationMinutes": "number",
        "environments": ["string"],
        "theme": "string", // 'system' | 'light' | 'dark'
        "notifications": "boolean",
        "language": "string",
        "units": "string",
        "onboardingComplete": "boolean",
        "fitnessGoalsSet": "boolean",
        "comprehensiveOnboardingComplete": "boolean"
      },
      
      // User Stats
      "stats": {
        "totalWorkouts": "number",
        "totalMinutes": "number", 
        "totalCaloriesBurned": "number",
        "currentStreak": "number",
        "longestStreak": "number",
        "weeklyGoal": "number",
        "lastWorkoutDate": "string?",
        "monthlyStats": "object"
      },
      
      // Timestamps
      "createdAt": "timestamp",
      "updatedAt": "timestamp",
      "lastUpdated": "timestamp"
    }
  }
}
```

## 🚀 Features

### ✅ Implemented Features

1. **Progressive Onboarding Flow**
   - 5-step guided setup process
   - Progress tracking with visual indicators
   - Step validation and data persistence

2. **Personal Information Collection**
   - Date of birth with age calculation
   - Gender selection (Male/Female/Other)
   - Height and weight with unit preferences
   - BMI calculation and categorization

3. **Fitness Goals Setup**
   - Fitness level assessment
   - Activity level selection
   - Multiple fitness goals selection
   - Equipment availability tracking
   - Workout preferences (duration, frequency)

4. **App Preferences Configuration**
   - Workout environment selection
   - Theme preferences (System/Light/Dark)
   - Measurement units (Metric/Imperial)
   - Language selection
   - Notification settings

5. **Data Validation & Persistence**
   - Real-time form validation
   - Step-by-step data saving
   - Error handling and recovery
   - Onboarding completion tracking

6. **Firebase Integration**
   - Firestore document structure
   - Real-time data synchronization
   - User authentication integration
   - Analytics event tracking (ready)

## 🔄 User Flow

1. **User Signs Up** → Initial user profile created
2. **Auth Wrapper** → Checks onboarding status
3. **Onboarding Flow** → Guides through 5 steps:
   - Welcome & Introduction
   - Personal Information
   - Fitness Goals & Equipment
   - App Preferences
   - Completion Summary
4. **Data Persistence** → Each step saves to Firestore
5. **Completion** → User redirected to main app

## 🛠️ Usage Examples

### Check Onboarding Status
```dart
final isCompleted = await OnboardingService.hasCompletedOnboarding(userId);
```

### Update Onboarding Step
```dart
await OnboardingService.updateOnboardingStep(
  userId: userId,
  stepName: 'personalInfo',
  stepData: {'height': 175, 'weight': 70},
);
```

### Complete Onboarding
```dart
await OnboardingService.completeOnboarding(
  userId: userId,
  profile: userProfile,
  fitness: userFitness,
  preferences: userPreferences,
);
```

### Reset Onboarding (Testing)
```dart
await OnboardingService.resetOnboarding(userId);
```

## 🧪 Testing with MCP

You can test the onboarding system using your Firebase MCP:

### Create Test User
```dart
// Create a new user without onboarding completion
await OnboardingService.createInitialUserProfile(
  userId: 'test-user-onboarding',
  email: '<EMAIL>',
  displayName: 'Test User',
);
```

### Query Incomplete Onboarding Users
```dart
// Find users who haven't completed onboarding
final incompleteUsers = await firestore
  .collection('users')
  .where('onboardingCompleted', isEqualTo: false)
  .get();
```

## 🔧 Configuration

### Customizing Onboarding Steps

You can modify the onboarding flow by editing `OnboardingFlow.getDefaultSteps()` in `onboarding_step_model.dart`:

```dart
static List<OnboardingStep> getDefaultSteps() {
  return [
    // Add, remove, or modify steps here
    OnboardingStep(
      type: OnboardingStepType.customStep,
      title: 'Custom Step',
      subtitle: 'Custom subtitle',
      order: 5,
    ),
  ];
}
```

### Adding New Data Fields

1. Update `OnboardingData` model
2. Add UI components to relevant pages
3. Update validation logic
4. Modify Firestore schema if needed

## 🚀 Next Steps

1. **Test the Implementation**
   - Run the app and test the onboarding flow
   - Verify data persistence in Firestore
   - Test navigation and validation

2. **Customize for Your Needs**
   - Modify onboarding steps
   - Add custom validation rules
   - Integrate with your existing user model

3. **Add Analytics**
   - Track onboarding completion rates
   - Monitor step abandonment
   - A/B test different flows

4. **Enhance UX**
   - Add animations and transitions
   - Implement skip options for optional steps
   - Add progress saving and restoration

## 📱 Integration

The onboarding system is fully integrated with your existing:
- Firebase authentication
- User model structure
- State management (Riverpod)
- Theme system
- Navigation flow

Users will automatically be directed to onboarding after signup and to the main app after completion.
