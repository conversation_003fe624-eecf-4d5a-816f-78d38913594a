{
  // Example:
  //
  // "indexes": [
  //   {
  //     "collectionGroup": "widgets",
  //     "queryScope": "COLLECTION",
  //     "fields": [
  //       { "fieldPath": "foo", "arrayConfig": "CONTAINS" },
  //       { "fieldPath": "bar", "mode": "DESCENDING" }
  //     ]
  //   },
  //
  //  "fieldOverrides": [
  //    {
  //      "collectionGroup": "widgets",
  //      "fieldPath": "baz",
  //      "indexes": [
  //        { "order": "ASCENDING", "queryScope": "COLLECTION" }
  //      ]
  //    },
  //   ]
  // ]
  "indexes": [],
  "fieldOverrides": []
}