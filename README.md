# OpenFit - Fitness Tracking App

A scalable and maintainable Flutter fitness tracking application with a clean architecture.

## 🏗️ Architecture Overview

This project follows a **feature-based architecture** with clean separation of concerns, making it highly scalable and maintainable as you add new features.

### 📁 Project Structure

```
lib/
├── main.dart                    # App entry point
├── app/
│   └── app.dart                # Main app widget with theme configuration
├── core/                       # Shared core functionality
│   ├── constants/
│   │   └── app_constants.dart  # App-wide constants
│   ├── theme/
│   │   ├── app_theme.dart      # Theme configuration
│   │   ├── colors.dart         # Color palette
│   │   └── text_styles.dart    # Typography definitions
│   └── utils/
│       ├── validators.dart     # Input validation utilities
│       └── formatters.dart     # Data formatting utilities
├── features/                   # Feature-based modules
│   ├── home/
│   │   └── presentation/
│   │       └── pages/
│   │           └── home_page.dart
│   ├── dashboard/
│   │   └── presentation/
│   │       ├── pages/
│   │       │   └── dashboard_page.dart
│   │       └── widgets/
│   │           ├── activity_summary_card.dart
│   │           ├── recent_workouts_list.dart
│   │           └── nutrition_summary_card.dart
│   ├── workouts/
│   ├── nutrition/
│   └── profile/
└── shared/                     # Reusable components
    └── widgets/
        ├── cards/
        │   ├── metric_card.dart
        │   └── workout_card.dart
        └── progress/
            └── nutrient_progress.dart
```

## 🚀 Key Features

- **Feature-based Architecture**: Each feature is self-contained and independent
- **Clean Separation**: Clear separation between UI, business logic, and data layers
- **Reusable Components**: Shared widgets for consistent UI across features
- **Theme System**: Centralized theme management with Material 3 support
- **Utility Functions**: Common validators and formatters for data handling
- **Scalable Structure**: Easy to add new features without affecting existing code

## 🎨 Design System

### Colors
- **Primary**: Green (#4CAF50) - Fitness theme
- **Semantic Colors**: Success, Warning, Error, Info
- **Fitness Specific**: Cardio, Strength, Flexibility, Nutrition colors

### Typography
- Material 3 compliant text styles
- Consistent spacing and sizing
- Optimized for readability

## 🧪 Testing

The project includes:
- Widget tests for UI components
- Unit tests for utilities and business logic
- Integration tests for feature workflows

Run tests with:
```bash
flutter test
```

## 📦 Dependencies

### Core Dependencies
- `flutter`: Flutter SDK
- `cupertino_icons`: iOS-style icons
- `intl`: Internationalization support

### Firebase & Backend
- `firebase_core`: Firebase SDK core
- `firebase_auth`: Authentication
- `cloud_firestore`: NoSQL database
- `firebase_storage`: File storage
- `firebase_analytics`: Analytics

### State Management & Architecture
- `flutter_riverpod`: State management
- `dio`: HTTP client
- `shared_preferences`: Local storage

### Development Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Linting rules

## 🔧 Development Guidelines

### Adding New Features

1. **Create Feature Folder**: Add new feature under `lib/features/`
2. **Follow Structure**: Use the same folder structure as existing features
3. **Shared Components**: Place reusable widgets in `lib/shared/widgets/`
4. **Constants**: Add feature-specific constants to `lib/core/constants/`

### Code Organization

- **Pages**: Main screens for each feature
- **Widgets**: Feature-specific UI components
- **Models**: Data structures (to be added)
- **Repositories**: Data access layer (to be added)
- **Providers/Blocs**: State management (to be added)

### Best Practices

1. **Single Responsibility**: Each file should have a single, clear purpose
2. **Consistent Naming**: Use descriptive names following Dart conventions
3. **Reusability**: Extract common UI patterns into shared widgets
4. **Testing**: Write tests for new features and components
5. **Documentation**: Document complex logic and public APIs

## 🚀 Getting Started

1. **Clone the repository**
2. **Install dependencies**:
   ```bash
   flutter pub get
   ```
3. **Run the app**:
   ```bash
   flutter run
   ```
4. **Run tests**:
   ```bash
   flutter test
   ```
5. **Analyze code**:
   ```bash
   flutter analyze
   ```

## 🔮 Future Enhancements

The current structure is ready for:
- **State Management**: Add Riverpod, Bloc, or Provider
- **Navigation**: Implement go_router for advanced routing
- **API Integration**: Add HTTP client and data repositories
- **Local Storage**: Implement Hive or SQLite for offline data
- **Authentication**: Add user authentication flow
- **Internationalization**: Support multiple languages

## 📱 Current Features

### 🔐 Authentication
- **Email/Password Sign Up & Sign In**: Secure user registration and login
- **User Profile Management**: Store user information in Firestore
- **Password Reset**: Email-based password recovery
- **Auth State Management**: Automatic login/logout handling

### 📊 Dashboard
- **Activity Summary**: Daily steps, calories, and exercise time
- **Recent Workouts**: Horizontal scrollable workout cards
- **Nutrition Tracking**: Calorie and macronutrient progress
- **User Greeting**: Personalized welcome message

### 🏗️ Architecture
- **Feature-Based Structure**: Organized by functionality
- **Firebase Integration**: Complete backend setup
- **State Management**: Riverpod for reactive state
- **Theme Support**: Light and dark mode with Material 3
- **Responsive Design**: Optimized for different screen sizes

### 🔥 Firebase Services
- **Firestore Database**: User profiles, workouts, nutrition data
- **Authentication**: Secure user management
- **Storage**: Profile pictures and media files
- **Analytics**: User behavior tracking (ready to use)

## 🚀 Firebase Setup

Your app is connected to Firebase project: **Openfit-AI**

### Configured Services:
- ✅ **Authentication**: Email/password authentication
- ✅ **Firestore**: NoSQL database for app data
- ✅ **Storage**: File storage for images and media
- ✅ **Analytics**: User behavior tracking

### Database Structure:
```
users/
  {userId}/
    - uid, email, name, createdAt, updatedAt
    - profilePictureUrl, dateOfBirth, height, weight
    - fitnessGoals[], activityLevel

    workouts/
      {workoutId}/
        - name, type, duration, caloriesBurned
        - exercises[], date, notes

    meals/
      {mealId}/
        - date, totalCalories, totalProtein, totalCarbs, totalFat
        - meals[], waterIntake

    activities/
      {activityId}/
        - type, duration, caloriesBurned, date
```

This architecture provides a solid foundation for building a comprehensive fitness tracking application that can scale with your needs.
