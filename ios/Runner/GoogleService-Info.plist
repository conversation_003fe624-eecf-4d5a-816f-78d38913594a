<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>745521622245-l6sa2n2lqar3hvlq3di31o7pmbdmc6vp.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.745521622245-l6sa2n2lqar3hvlq3di31o7pmbdmc6vp</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>745521622245-pmpardblmhg7u4191dlp4glucmj2a9o3.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCWq1nvA-BWpCIBm-NwRYXHetnOK4n1_as</string>
	<key>GCM_SENDER_ID</key>
	<string>745521622245</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.openfit.openfit</string>
	<key>PROJECT_ID</key>
	<string>po2vf2ae7tal9invaj7jkf4a06hsac</string>
	<key>STORAGE_BUCKET</key>
	<string>po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:745521622245:ios:4af840b9e0d1ed089c780c</string>
</dict>
</plist>