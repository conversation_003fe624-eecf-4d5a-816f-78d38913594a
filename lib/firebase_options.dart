// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyARfH5uNjIGk7FIYtSZm4Ou2cosfMhysKw',
    appId: '1:745521622245:android:eace8279975105f09c780c',
    messagingSenderId: '745521622245',
    projectId: 'po2vf2ae7tal9invaj7jkf4a06hsac',
    storageBucket: 'po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCWq1nvA-BWpCIBm-NwRYXHetnOK4n1_as',
    appId: '1:745521622245:ios:4af840b9e0d1ed089c780c',
    messagingSenderId: '745521622245',
    projectId: 'po2vf2ae7tal9invaj7jkf4a06hsac',
    storageBucket: 'po2vf2ae7tal9invaj7jkf4a06hsac.firebasestorage.app',
    androidClientId: '745521622245-pmpardblmhg7u4191dlp4glucmj2a9o3.apps.googleusercontent.com',
    iosClientId: '745521622245-l6sa2n2lqar3hvlq3di31o7pmbdmc6vp.apps.googleusercontent.com',
    iosBundleId: 'com.openfit.openfit',
  );
}
