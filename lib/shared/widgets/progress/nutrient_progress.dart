import 'package:flutter/material.dart';

class NutrientProgress extends StatelessWidget {
  final String label;
  final double value;
  final String amount;

  const NutrientProgress({
    super.key,
    required this.label,
    required this.value,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(label),
        const SizedBox(height: 8),
        SizedBox(
          height: 50,
          width: 50,
          child: CircularProgressIndicator(
            value: value,
            strokeWidth: 8,
            backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
          ),
        ),
        const SizedBox(height: 8),
        Text(amount),
      ],
    );
  }
}
