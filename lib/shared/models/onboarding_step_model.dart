enum OnboardingStepType {
  welcome,
  personalInfo,
  fitnessGoals,
  preferences,
  completion,
}

class OnboardingStep {
  final OnboardingStepType type;
  final String title;
  final String subtitle;
  final String? description;
  final bool isCompleted;
  final bool isRequired;
  final int order;
  final Map<String, dynamic> data;

  OnboardingStep({
    required this.type,
    required this.title,
    required this.subtitle,
    this.description,
    this.isCompleted = false,
    this.isRequired = true,
    required this.order,
    this.data = const {},
  });

  OnboardingStep copyWith({
    OnboardingStepType? type,
    String? title,
    String? subtitle,
    String? description,
    bool? isCompleted,
    bool? isRequired,
    int? order,
    Map<String, dynamic>? data,
  }) {
    return OnboardingStep(
      type: type ?? this.type,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      isRequired: isRequired ?? this.isRequired,
      order: order ?? this.order,
      data: data ?? this.data,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'isCompleted': isCompleted,
      'isRequired': isRequired,
      'order': order,
      'data': data,
    };
  }

  factory OnboardingStep.fromMap(Map<String, dynamic> map) {
    return OnboardingStep(
      type: OnboardingStepType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => OnboardingStepType.welcome,
      ),
      title: map['title'] ?? '',
      subtitle: map['subtitle'] ?? '',
      description: map['description'],
      isCompleted: map['isCompleted'] ?? false,
      isRequired: map['isRequired'] ?? true,
      order: map['order'] ?? 0,
      data: Map<String, dynamic>.from(map['data'] ?? {}),
    );
  }
}

class OnboardingFlow {
  static List<OnboardingStep> getDefaultSteps() {
    return [
      OnboardingStep(
        type: OnboardingStepType.welcome,
        title: 'Welcome to OpenFit',
        subtitle: 'Your AI-powered fitness companion',
        description: 'Let\'s get you started on your fitness journey with personalized workouts and nutrition guidance.',
        order: 0,
      ),
      OnboardingStep(
        type: OnboardingStepType.personalInfo,
        title: 'Personal Information',
        subtitle: 'Tell us about yourself',
        description: 'This helps us create personalized recommendations just for you.',
        order: 1,
      ),
      OnboardingStep(
        type: OnboardingStepType.fitnessGoals,
        title: 'Fitness Goals',
        subtitle: 'What do you want to achieve?',
        description: 'Set your goals so we can tailor your experience.',
        order: 2,
      ),
      OnboardingStep(
        type: OnboardingStepType.preferences,
        title: 'Preferences',
        subtitle: 'Customize your experience',
        description: 'Set your workout preferences and app settings.',
        order: 3,
      ),
      OnboardingStep(
        type: OnboardingStepType.completion,
        title: 'You\'re All Set!',
        subtitle: 'Welcome to your fitness journey',
        description: 'Everything is ready. Let\'s start achieving your goals!',
        order: 4,
        isRequired: false,
      ),
    ];
  }
}
