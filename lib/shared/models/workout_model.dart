import 'package:cloud_firestore/cloud_firestore.dart';

// Workout Plan Model - for predefined workout templates
class WorkoutPlanModel {
  final String id;
  final String name;
  final String description;
  final String category;
  final String difficulty;
  final int duration; // in minutes
  final List<String> equipment;
  final List<String> targetMuscleGroups;
  final List<WorkoutExercise> exercises;
  final List<String> warmup;
  final List<String> cooldown;
  final bool isPublic;
  final bool isCustom;
  final String? createdBy;
  final List<String> tags;
  final String? imageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  WorkoutPlanModel({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.duration,
    this.equipment = const [],
    this.targetMuscleGroups = const [],
    this.exercises = const [],
    this.warmup = const [],
    this.cooldown = const [],
    this.isPublic = true,
    this.isCustom = false,
    this.createdBy,
    this.tags = const [],
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
  });

  factory WorkoutPlanModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkoutPlanModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      category: data['category'] ?? '',
      difficulty: data['difficulty'] ?? 'beginner',
      duration: data['duration'] ?? 30,
      equipment: List<String>.from(data['equipment'] ?? []),
      targetMuscleGroups: List<String>.from(data['targetMuscleGroups'] ?? []),
      exercises: (data['exercises'] as List<dynamic>?)
              ?.map((e) => WorkoutExercise.fromMap(e as Map<String, dynamic>))
              .toList() ??
          [],
      warmup: List<String>.from(data['warmup'] ?? []),
      cooldown: List<String>.from(data['cooldown'] ?? []),
      isPublic: data['isPublic'] ?? true,
      isCustom: data['isCustom'] ?? false,
      createdBy: data['createdBy'],
      tags: List<String>.from(data['tags'] ?? []),
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'category': category,
      'difficulty': difficulty,
      'duration': duration,
      'equipment': equipment,
      'targetMuscleGroups': targetMuscleGroups,
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'warmup': warmup,
      'cooldown': cooldown,
      'isPublic': isPublic,
      'isCustom': isCustom,
      'createdBy': createdBy,
      'tags': tags,
      'imageUrl': imageUrl,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }
}

// Workout History Model - for completed workouts
class WorkoutHistoryModel {
  final String id;
  final String userId;
  final String? workoutPlanId;
  final String? workoutPlanName;
  final DateTime startTime;
  final DateTime endTime;
  final int duration; // in minutes
  final List<CompletedExercise> exercises;
  final List<String> musclesWorked;
  final int totalCaloriesBurned;
  final String difficulty;
  final bool isCompleted;
  final String? notes;
  final String? overallNotes;
  final DateTime completedAt;
  final DateTime createdAt;

  WorkoutHistoryModel({
    required this.id,
    required this.userId,
    this.workoutPlanId,
    this.workoutPlanName,
    required this.startTime,
    required this.endTime,
    required this.duration,
    this.exercises = const [],
    this.musclesWorked = const [],
    required this.totalCaloriesBurned,
    required this.difficulty,
    this.isCompleted = true,
    this.notes,
    this.overallNotes,
    required this.completedAt,
    required this.createdAt,
  });

  factory WorkoutHistoryModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WorkoutHistoryModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      workoutPlanId: data['workoutPlanId'],
      workoutPlanName: data['workoutPlanName'],
      startTime: _parseDateTime(data['startTime']),
      endTime: _parseDateTime(data['endTime']),
      duration: data['duration'] ?? 0,
      exercises: (data['exercises'] as List<dynamic>?)
              ?.map((e) => CompletedExercise.fromMap(e as Map<String, dynamic>))
              .toList() ??
          [],
      musclesWorked: List<String>.from(data['musclesWorked'] ?? []),
      totalCaloriesBurned: data['totalCaloriesBurned'] ?? 0,
      difficulty: data['difficulty'] ?? 'beginner',
      isCompleted: data['isCompleted'] ?? true,
      notes: data['notes'],
      overallNotes: data['overallNotes'],
      completedAt: (data['completedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value is Timestamp) {
      return value.toDate();
    } else if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }
    return DateTime.now();
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'workoutPlanId': workoutPlanId,
      'workoutPlanName': workoutPlanName,
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'duration': duration,
      'exercises': exercises.map((e) => e.toMap()).toList(),
      'musclesWorked': musclesWorked,
      'totalCaloriesBurned': totalCaloriesBurned,
      'difficulty': difficulty,
      'isCompleted': isCompleted,
      'notes': notes,
      'overallNotes': overallNotes,
      'completedAt': Timestamp.fromDate(completedAt),
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

class WorkoutExercise {
  final String exerciseId;
  final int sets;
  final dynamic reps; // can be string or number
  final int? duration;
  final int restSeconds;
  final int? restTime;
  final String? notes;

  WorkoutExercise({
    required this.exerciseId,
    required this.sets,
    required this.reps,
    this.duration,
    required this.restSeconds,
    this.restTime,
    this.notes,
  });

  factory WorkoutExercise.fromMap(Map<String, dynamic> map) {
    return WorkoutExercise(
      exerciseId: map['exerciseId'] ?? '',
      sets: map['sets'] ?? 1,
      reps: map['reps'],
      duration: map['duration'],
      restSeconds: map['restSeconds'] ?? 60,
      restTime: map['restTime'],
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'exerciseId': exerciseId,
      'sets': sets,
      'reps': reps,
      'duration': duration,
      'restSeconds': restSeconds,
      'restTime': restTime,
      'notes': notes,
    };
  }
}

class CompletedExercise {
  final String exerciseId;
  final String exerciseName;
  final List<CompletedSet> setsCompleted;
  final String? notes;

  CompletedExercise({
    required this.exerciseId,
    required this.exerciseName,
    this.setsCompleted = const [],
    this.notes,
  });

  factory CompletedExercise.fromMap(Map<String, dynamic> map) {
    return CompletedExercise(
      exerciseId: map['exerciseId'] ?? '',
      exerciseName: map['exerciseName'] ?? '',
      setsCompleted: (map['setsCompleted'] as List<dynamic>?)
              ?.map((s) => CompletedSet.fromMap(s as Map<String, dynamic>))
              .toList() ??
          [],
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'exerciseId': exerciseId,
      'exerciseName': exerciseName,
      'setsCompleted': setsCompleted.map((s) => s.toMap()).toList(),
      'notes': notes,
    };
  }
}

class CompletedSet {
  final int reps;
  final double? weight; // in kg
  final int restTaken; // in seconds

  CompletedSet({
    required this.reps,
    this.weight,
    required this.restTaken,
  });

  factory CompletedSet.fromMap(Map<String, dynamic> map) {
    return CompletedSet(
      reps: map['reps'] ?? 0,
      weight: map['weight']?.toDouble(),
      restTaken: map['restTaken'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'reps': reps,
      'weight': weight,
      'restTaken': restTaken,
    };
  }
}

enum WorkoutCategory {
  strength,
  cardio,
  hiit,
  fullBody,
  upperBody,
  lowerBody,
  core,
  flexibility,
  sports,
}

enum WorkoutDifficulty {
  beginner,
  intermediate,
  advanced,
}

enum WorkoutEquipment {
  none,
  dumbbells,
  barbell,
  kettlebell,
  resistanceBands,
  pullUpBar,
  bench,
  machine,
}
