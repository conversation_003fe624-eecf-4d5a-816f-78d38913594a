import 'package:cloud_firestore/cloud_firestore.dart';

class UserFitnessGuideModel {
  final String id;
  final String userId;
  final int version;
  final DateTime generatedAt;
  final FitnessGuide guide;

  UserFitnessGuideModel({
    required this.id,
    required this.userId,
    required this.version,
    required this.generatedAt,
    required this.guide,
  });

  factory UserFitnessGuideModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserFitnessGuideModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      version: data['version'] ?? 1,
      generatedAt: (data['generatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      guide: FitnessGuide.fromMap(data['guide'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'version': version,
      'generatedAt': Timestamp.fromDate(generatedAt),
      'guide': guide.toMap(),
    };
  }
}

class FitnessGuide {
  final String introduction;
  final String goalStrategy;
  final String workoutPrinciples;
  final String progressionPlan;
  final String recoveryGuidelines;
  final List<WorkoutSplitOption> workoutSplitOptions;

  FitnessGuide({
    required this.introduction,
    required this.goalStrategy,
    required this.workoutPrinciples,
    required this.progressionPlan,
    required this.recoveryGuidelines,
    this.workoutSplitOptions = const [],
  });

  factory FitnessGuide.fromMap(Map<String, dynamic> map) {
    return FitnessGuide(
      introduction: map['introduction'] ?? '',
      goalStrategy: map['goalStrategy'] ?? '',
      workoutPrinciples: map['workoutPrinciples'] ?? '',
      progressionPlan: map['progressionPlan'] ?? '',
      recoveryGuidelines: map['recoveryGuidelines'] ?? '',
      workoutSplitOptions: (map['workoutSplitOptions'] as List<dynamic>?)
              ?.map((option) => WorkoutSplitOption.fromMap(option as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'introduction': introduction,
      'goalStrategy': goalStrategy,
      'workoutPrinciples': workoutPrinciples,
      'progressionPlan': progressionPlan,
      'recoveryGuidelines': recoveryGuidelines,
      'workoutSplitOptions': workoutSplitOptions.map((option) => option.toMap()).toList(),
    };
  }
}

class WorkoutSplitOption {
  final String name;
  final String description;
  final List<String> schedule;

  WorkoutSplitOption({
    required this.name,
    required this.description,
    this.schedule = const [],
  });

  factory WorkoutSplitOption.fromMap(Map<String, dynamic> map) {
    return WorkoutSplitOption(
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      schedule: List<String>.from(map['schedule'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'schedule': schedule,
    };
  }
}

enum WorkoutSplitType {
  fullBody,
  upperLower,
  pushPullLegs,
  bodyPart,
  custom,
}

class FitnessKnowledgeVector {
  final String id;
  final String content;
  final List<double> embedding;
  final String category;
  final String title;
  final List<String> tags;
  final VectorMetadata metadata;

  FitnessKnowledgeVector({
    required this.id,
    required this.content,
    required this.embedding,
    required this.category,
    required this.title,
    this.tags = const [],
    required this.metadata,
  });

  factory FitnessKnowledgeVector.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FitnessKnowledgeVector(
      id: doc.id,
      content: data['content'] ?? '',
      embedding: List<double>.from(data['embedding'] ?? []),
      category: data['category'] ?? '',
      title: data['title'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      metadata: VectorMetadata.fromMap(data['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'content': content,
      'embedding': embedding,
      'category': category,
      'title': title,
      'tags': tags,
      'metadata': metadata.toMap(),
    };
  }
}

class VectorMetadata {
  final String documentType;
  final DateTime indexedAt;

  VectorMetadata({
    required this.documentType,
    required this.indexedAt,
  });

  factory VectorMetadata.fromMap(Map<String, dynamic> map) {
    return VectorMetadata(
      documentType: map['documentType'] ?? '',
      indexedAt: (map['indexedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'documentType': documentType,
      'indexedAt': Timestamp.fromDate(indexedAt),
    };
  }
}
