import 'user_model.dart';
import 'user_fitness_model.dart';
import 'user_preferences_model.dart';

class OnboardingData {
  // Personal Information
  DateTime? dateOfBirth;
  String? gender;
  double? height; // in cm
  double? weight; // in kg
  String? preferredUnits;

  // Fitness Information
  String? fitnessLevel;
  List<String> fitnessGoals;
  List<String> targetMuscleGroups;
  String? activityLevel;
  List<String> availableEquipment;
  List<String> preferredWorkoutTypes;
  int? workoutDuration; // in minutes
  int? workoutsPerWeek;

  // Preferences
  List<String> workoutEnvironments;
  String? theme;
  bool notifications;
  String? language;
  String? units;

  // Health & Safety
  List<String> healthConditions;
  List<String> injuries;
  bool hasHealthRestrictions;

  OnboardingData({
    this.dateOfBirth,
    this.gender,
    this.height,
    this.weight,
    this.preferredUnits = 'metric',
    this.fitnessLevel,
    this.fitnessGoals = const [],
    this.targetMuscleGroups = const [],
    this.activityLevel,
    this.availableEquipment = const [],
    this.preferredWorkoutTypes = const [],
    this.workoutDuration,
    this.workoutsPerWeek,
    this.workoutEnvironments = const ['homeNoEquipment'],
    this.theme = 'system',
    this.notifications = true,
    this.language = 'en',
    this.units = 'metric',
    this.healthConditions = const [],
    this.injuries = const [],
    this.hasHealthRestrictions = false,
  });

  // Convert to UserProfile
  UserProfile toUserProfile() {
    return UserProfile(
      dateOfBirth: dateOfBirth,
      gender: gender ?? 'male',
      height: height,
      weight: weight,
      preferredUnits: preferredUnits ?? 'metric',
    );
  }

  // Convert to UserFitness
  UserFitness toUserFitness() {
    // Convert fitness goals to FitnessGoal objects
    final goals = fitnessGoals.map((goal) {
      return FitnessGoal(
        type: _mapGoalToType(goal),
        priority: fitnessGoals.indexOf(goal) + 1,
        selectedAt: DateTime.now(),
      );
    }).toList();

    return UserFitness(
      cardioLevel: _mapActivityToCardioLevel(activityLevel),
      strengthLevel: _mapFitnessLevelToStrength(fitnessLevel),
      flexibilityLevel: 0.5, // Default flexibility level
      goals: goals,
      exercisesToAvoid: injuries,
      hasPersonalizedGuide: false,
    );
  }

  // Helper method to map goal strings to types
  String _mapGoalToType(String goal) {
    switch (goal.toLowerCase()) {
      case 'lose weight':
        return 'weightLoss';
      case 'build muscle':
        return 'muscleGain';
      case 'improve endurance':
        return 'endurance';
      case 'increase strength':
        return 'strength';
      case 'improve flexibility':
        return 'flexibility';
      case 'general health':
        return 'healthOptimization';
      default:
        return 'generalFitness';
    }
  }

  // Helper method to map activity level to cardio level
  double _mapActivityToCardioLevel(String? activityLevel) {
    switch (activityLevel?.toLowerCase()) {
      case 'sedentary':
        return 0.2;
      case 'light':
        return 0.4;
      case 'moderate':
        return 0.6;
      case 'active':
        return 0.8;
      case 'very_active':
        return 1.0;
      default:
        return 0.5;
    }
  }

  // Helper method to map fitness level to strength level
  double _mapFitnessLevelToStrength(String? fitnessLevel) {
    switch (fitnessLevel?.toLowerCase()) {
      case 'beginner':
        return 0.3;
      case 'intermediate':
        return 0.6;
      case 'advanced':
        return 0.9;
      default:
        return 0.5;
    }
  }

  // Convert to UserPreferences
  UserPreferences toUserPreferences() {
    return UserPreferences(
      workoutsPerWeek: workoutsPerWeek ?? 3,
      durationMinutes: workoutDuration ?? 30,
      environments: workoutEnvironments,
      theme: theme ?? 'system',
      notifications: notifications,
      language: language ?? 'en',
      units: units ?? 'metric',
      onboardingComplete: true,
      fitnessGoalsSet: fitnessGoals.isNotEmpty,
      comprehensiveOnboardingComplete: true,
    );
  }

  // Validation methods
  bool get isPersonalInfoComplete {
    return dateOfBirth != null && 
           gender != null && 
           height != null && 
           weight != null;
  }

  bool get isFitnessInfoComplete {
    return fitnessLevel != null && 
           fitnessGoals.isNotEmpty && 
           activityLevel != null;
  }

  bool get isPreferencesComplete {
    return workoutEnvironments.isNotEmpty && 
           workoutDuration != null && 
           workoutsPerWeek != null;
  }

  bool get isComplete {
    return isPersonalInfoComplete && 
           isFitnessInfoComplete && 
           isPreferencesComplete;
  }

  // Calculate BMI
  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  // Get BMI category
  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal weight';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  // Calculate age
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  Map<String, dynamic> toMap() {
    return {
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'height': height,
      'weight': weight,
      'preferredUnits': preferredUnits,
      'fitnessLevel': fitnessLevel,
      'fitnessGoals': fitnessGoals,
      'targetMuscleGroups': targetMuscleGroups,
      'activityLevel': activityLevel,
      'availableEquipment': availableEquipment,
      'preferredWorkoutTypes': preferredWorkoutTypes,
      'workoutDuration': workoutDuration,
      'workoutsPerWeek': workoutsPerWeek,
      'workoutEnvironments': workoutEnvironments,
      'theme': theme,
      'notifications': notifications,
      'language': language,
      'units': units,
      'healthConditions': healthConditions,
      'injuries': injuries,
      'hasHealthRestrictions': hasHealthRestrictions,
    };
  }

  factory OnboardingData.fromMap(Map<String, dynamic> map) {
    return OnboardingData(
      dateOfBirth: map['dateOfBirth'] != null 
          ? DateTime.parse(map['dateOfBirth']) 
          : null,
      gender: map['gender'],
      height: map['height']?.toDouble(),
      weight: map['weight']?.toDouble(),
      preferredUnits: map['preferredUnits'] ?? 'metric',
      fitnessLevel: map['fitnessLevel'],
      fitnessGoals: List<String>.from(map['fitnessGoals'] ?? []),
      targetMuscleGroups: List<String>.from(map['targetMuscleGroups'] ?? []),
      activityLevel: map['activityLevel'],
      availableEquipment: List<String>.from(map['availableEquipment'] ?? []),
      preferredWorkoutTypes: List<String>.from(map['preferredWorkoutTypes'] ?? []),
      workoutDuration: map['workoutDuration'],
      workoutsPerWeek: map['workoutsPerWeek'],
      workoutEnvironments: List<String>.from(map['workoutEnvironments'] ?? ['homeNoEquipment']),
      theme: map['theme'] ?? 'system',
      notifications: map['notifications'] ?? true,
      language: map['language'] ?? 'en',
      units: map['units'] ?? 'metric',
      healthConditions: List<String>.from(map['healthConditions'] ?? []),
      injuries: List<String>.from(map['injuries'] ?? []),
      hasHealthRestrictions: map['hasHealthRestrictions'] ?? false,
    );
  }

  OnboardingData copyWith({
    DateTime? dateOfBirth,
    String? gender,
    double? height,
    double? weight,
    String? preferredUnits,
    String? fitnessLevel,
    List<String>? fitnessGoals,
    List<String>? targetMuscleGroups,
    String? activityLevel,
    List<String>? availableEquipment,
    List<String>? preferredWorkoutTypes,
    int? workoutDuration,
    int? workoutsPerWeek,
    List<String>? workoutEnvironments,
    String? theme,
    bool? notifications,
    String? language,
    String? units,
    List<String>? healthConditions,
    List<String>? injuries,
    bool? hasHealthRestrictions,
  }) {
    return OnboardingData(
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      preferredUnits: preferredUnits ?? this.preferredUnits,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      targetMuscleGroups: targetMuscleGroups ?? this.targetMuscleGroups,
      activityLevel: activityLevel ?? this.activityLevel,
      availableEquipment: availableEquipment ?? this.availableEquipment,
      preferredWorkoutTypes: preferredWorkoutTypes ?? this.preferredWorkoutTypes,
      workoutDuration: workoutDuration ?? this.workoutDuration,
      workoutsPerWeek: workoutsPerWeek ?? this.workoutsPerWeek,
      workoutEnvironments: workoutEnvironments ?? this.workoutEnvironments,
      theme: theme ?? this.theme,
      notifications: notifications ?? this.notifications,
      language: language ?? this.language,
      units: units ?? this.units,
      healthConditions: healthConditions ?? this.healthConditions,
      injuries: injuries ?? this.injuries,
      hasHealthRestrictions: hasHealthRestrictions ?? this.hasHealthRestrictions,
    );
  }
}
