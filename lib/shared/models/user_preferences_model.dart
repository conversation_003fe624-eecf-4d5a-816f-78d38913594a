class UserPreferences {
  final int workoutsPerWeek;
  final int durationMinutes;
  final List<String> environments;
  final String theme;
  final bool notifications;
  final String language;
  final Map<String, dynamic> appSettings;
  final String units;
  final bool onboardingComplete;
  final bool fitnessGoalsSet;
  final bool comprehensiveOnboardingComplete;

  UserPreferences({
    this.workoutsPerWeek = 3,
    this.durationMinutes = 30,
    this.environments = const ['homeNoEquipment'],
    this.theme = 'system',
    this.notifications = true,
    this.language = 'en',
    this.appSettings = const {},
    this.units = 'metric',
    this.onboardingComplete = false,
    this.fitnessGoalsSet = false,
    this.comprehensiveOnboardingComplete = false,
  });

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      workoutsPerWeek: _toInt(map['workoutsPerWeek']) ?? 3,
      durationMinutes: _toInt(map['durationMinutes']) ?? 30,
      environments: List<String>.from(map['environments'] ?? ['homeNoEquipment']),
      theme: map['theme'] ?? 'system',
      notifications: map['notifications'] ?? true,
      language: map['language'] ?? 'en',
      appSettings: Map<String, dynamic>.from(map['appSettings'] ?? {}),
      units: map['units'] ?? 'metric',
      onboardingComplete: map['onboardingComplete'] ?? false,
      fitnessGoalsSet: map['fitnessGoalsSet'] ?? false,
      comprehensiveOnboardingComplete: map['comprehensiveOnboardingComplete'] ?? false,
    );
  }

  // Helper method to safely convert to int
  static int? _toInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value);
    return null;
  }

  Map<String, dynamic> toMap() {
    return {
      'workoutsPerWeek': workoutsPerWeek,
      'durationMinutes': durationMinutes,
      'environments': environments,
      'theme': theme,
      'notifications': notifications,
      'language': language,
      'appSettings': appSettings,
      'units': units,
      'onboardingComplete': onboardingComplete,
      'fitnessGoalsSet': fitnessGoalsSet,
      'comprehensiveOnboardingComplete': comprehensiveOnboardingComplete,
    };
  }

  UserPreferences copyWith({
    int? workoutsPerWeek,
    int? durationMinutes,
    List<String>? environments,
    String? theme,
    bool? notifications,
    String? language,
    Map<String, dynamic>? appSettings,
    String? units,
    bool? onboardingComplete,
    bool? fitnessGoalsSet,
    bool? comprehensiveOnboardingComplete,
  }) {
    return UserPreferences(
      workoutsPerWeek: workoutsPerWeek ?? this.workoutsPerWeek,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      environments: environments ?? this.environments,
      theme: theme ?? this.theme,
      notifications: notifications ?? this.notifications,
      language: language ?? this.language,
      appSettings: appSettings ?? this.appSettings,
      units: units ?? this.units,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      fitnessGoalsSet: fitnessGoalsSet ?? this.fitnessGoalsSet,
      comprehensiveOnboardingComplete: comprehensiveOnboardingComplete ?? this.comprehensiveOnboardingComplete,
    );
  }
}

enum WorkoutEnvironment {
  homeNoEquipment,
  homeWithEquipment,
  gym,
  largeGym,
  outdoor,
  pool,
}

enum ThemeMode {
  system,
  light,
  dark,
}

enum PreferredUnits {
  metric,
  imperial,
}
