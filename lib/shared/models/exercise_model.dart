import 'package:cloud_firestore/cloud_firestore.dart';

class ExerciseModel {
  final String id;
  final String name;
  final String description;
  final List<String> instructions;
  final String primaryMuscleGroup;
  final List<String> secondaryMuscleGroups;
  final List<String> muscleGroups;
  final List<String> equipment;
  final String difficulty;
  final String category;
  final int? duration;
  final double caloriesPerMinute;
  final bool isBodyweight;
  final String? videoUrl;
  final String? verticalVideoUrl;
  final String? imageUrl;
  final List<String> tags;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ExerciseModel({
    required this.id,
    required this.name,
    required this.description,
    this.instructions = const [],
    required this.primaryMuscleGroup,
    this.secondaryMuscleGroups = const [],
    this.muscleGroups = const [],
    this.equipment = const [],
    this.difficulty = 'beginner',
    required this.category,
    this.duration,
    this.caloriesPerMinute = 5.0,
    this.isBodyweight = false,
    this.videoUrl,
    this.verticalVideoUrl,
    this.imageUrl,
    this.tags = const [],
    this.createdAt,
    this.updatedAt,
  });

  factory ExerciseModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ExerciseModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      instructions: List<String>.from(data['instructions'] ?? []),
      primaryMuscleGroup: data['primaryMuscleGroup'] ?? '',
      secondaryMuscleGroups: List<String>.from(data['secondaryMuscleGroups'] ?? []),
      muscleGroups: List<String>.from(data['muscleGroups'] ?? []),
      equipment: List<String>.from(data['equipment'] ?? []),
      difficulty: data['difficulty'] ?? 'beginner',
      category: data['category'] ?? '',
      duration: data['duration'],
      caloriesPerMinute: data['caloriesPerMinute']?.toDouble() ?? 5.0,
      isBodyweight: data['isBodyweight'] ?? false,
      videoUrl: data['videoUrl'],
      verticalVideoUrl: data['verticalVideoUrl'],
      imageUrl: data['imageUrl'],
      tags: List<String>.from(data['tags'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'instructions': instructions,
      'primaryMuscleGroup': primaryMuscleGroup,
      'secondaryMuscleGroups': secondaryMuscleGroups,
      'muscleGroups': muscleGroups,
      'equipment': equipment,
      'difficulty': difficulty,
      'category': category,
      'duration': duration,
      'caloriesPerMinute': caloriesPerMinute,
      'isBodyweight': isBodyweight,
      'videoUrl': videoUrl,
      'verticalVideoUrl': verticalVideoUrl,
      'imageUrl': imageUrl,
      'tags': tags,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  ExerciseModel copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? instructions,
    String? primaryMuscleGroup,
    List<String>? secondaryMuscleGroups,
    List<String>? muscleGroups,
    List<String>? equipment,
    String? difficulty,
    String? category,
    int? duration,
    double? caloriesPerMinute,
    bool? isBodyweight,
    String? videoUrl,
    String? verticalVideoUrl,
    String? imageUrl,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExerciseModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      instructions: instructions ?? this.instructions,
      primaryMuscleGroup: primaryMuscleGroup ?? this.primaryMuscleGroup,
      secondaryMuscleGroups: secondaryMuscleGroups ?? this.secondaryMuscleGroups,
      muscleGroups: muscleGroups ?? this.muscleGroups,
      equipment: equipment ?? this.equipment,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      caloriesPerMinute: caloriesPerMinute ?? this.caloriesPerMinute,
      isBodyweight: isBodyweight ?? this.isBodyweight,
      videoUrl: videoUrl ?? this.videoUrl,
      verticalVideoUrl: verticalVideoUrl ?? this.verticalVideoUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get hasVideo => videoUrl != null && videoUrl!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get requiresEquipment => equipment.isNotEmpty && !isBodyweight;
  
  String get formattedDuration {
    if (duration == null) return 'Variable';
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    }
    return '${seconds}s';
  }
}

class ExerciseAlias {
  final String alias;
  final String exerciseId;

  ExerciseAlias({
    required this.alias,
    required this.exerciseId,
  });

  factory ExerciseAlias.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ExerciseAlias(
      alias: data['alias'] ?? '',
      exerciseId: data['exerciseId'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'alias': alias,
      'exerciseId': exerciseId,
    };
  }
}

enum ExerciseDifficulty {
  beginner,
  intermediate,
  advanced,
}

enum ExerciseCategory {
  strength,
  cardio,
  flexibility,
  balance,
  plyometric,
  core,
  warmup,
  cooldown,
}

enum MuscleGroup {
  chest,
  back,
  shoulders,
  biceps,
  triceps,
  forearms,
  abs,
  obliques,
  quadriceps,
  hamstrings,
  glutes,
  calves,
  fullBody,
}

enum Equipment {
  none,
  dumbbells,
  barbell,
  kettlebell,
  resistanceBands,
  pullUpBar,
  bench,
  machine,
  cable,
  medicineBall,
  foamRoller,
  yogaMat,
}
