import 'package:cloud_firestore/cloud_firestore.dart';

class NutritionVectorModel {
  final String id;
  final String content;
  final List<double> embedding;
  final String category;
  final String title;
  final List<String> tags;
  final NutritionVectorMetadata metadata;

  NutritionVectorModel({
    required this.id,
    required this.content,
    required this.embedding,
    required this.category,
    required this.title,
    this.tags = const [],
    required this.metadata,
  });

  factory NutritionVectorModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NutritionVectorModel(
      id: doc.id,
      content: data['content'] ?? '',
      embedding: List<double>.from(data['embedding'] ?? []),
      category: data['category'] ?? '',
      title: data['title'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      metadata: NutritionVectorMetadata.fromMap(data['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'content': content,
      'embedding': embedding,
      'category': category,
      'title': title,
      'tags': tags,
      'metadata': metadata.toMap(),
    };
  }
}

class NutritionVectorMetadata {
  final String documentType;
  final DateTime indexedAt;
  final String? source;
  final Map<String, dynamic> additionalData;

  NutritionVectorMetadata({
    required this.documentType,
    required this.indexedAt,
    this.source,
    this.additionalData = const {},
  });

  factory NutritionVectorMetadata.fromMap(Map<String, dynamic> map) {
    return NutritionVectorMetadata(
      documentType: map['documentType'] ?? '',
      indexedAt: (map['indexedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      source: map['source'],
      additionalData: Map<String, dynamic>.from(map['additionalData'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'documentType': documentType,
      'indexedAt': Timestamp.fromDate(indexedAt),
      'source': source,
      'additionalData': additionalData,
    };
  }
}

// Food database model for nutrition tracking
class FoodItemModel {
  final String id;
  final String name;
  final String brand;
  final String category;
  final NutritionFacts nutritionPer100g;
  final List<ServingSize> servingSizes;
  final String? barcode;
  final List<String> aliases;
  final bool isVerified;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  FoodItemModel({
    required this.id,
    required this.name,
    this.brand = '',
    required this.category,
    required this.nutritionPer100g,
    this.servingSizes = const [],
    this.barcode,
    this.aliases = const [],
    this.isVerified = false,
    this.createdAt,
    this.updatedAt,
  });

  factory FoodItemModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return FoodItemModel(
      id: doc.id,
      name: data['name'] ?? '',
      brand: data['brand'] ?? '',
      category: data['category'] ?? '',
      nutritionPer100g: NutritionFacts.fromMap(data['nutritionPer100g'] ?? {}),
      servingSizes: (data['servingSizes'] as List<dynamic>?)
              ?.map((s) => ServingSize.fromMap(s as Map<String, dynamic>))
              .toList() ??
          [],
      barcode: data['barcode'],
      aliases: List<String>.from(data['aliases'] ?? []),
      isVerified: data['isVerified'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'brand': brand,
      'category': category,
      'nutritionPer100g': nutritionPer100g.toMap(),
      'servingSizes': servingSizes.map((s) => s.toMap()).toList(),
      'barcode': barcode,
      'aliases': aliases,
      'isVerified': isVerified,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }
}

class NutritionFacts {
  final double calories;
  final double protein; // grams
  final double carbs; // grams
  final double fat; // grams
  final double fiber; // grams
  final double sugar; // grams
  final double sodium; // mg
  final double cholesterol; // mg
  final Map<String, double> vitamins; // vitamin name -> amount
  final Map<String, double> minerals; // mineral name -> amount

  NutritionFacts({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    this.fiber = 0,
    this.sugar = 0,
    this.sodium = 0,
    this.cholesterol = 0,
    this.vitamins = const {},
    this.minerals = const {},
  });

  factory NutritionFacts.fromMap(Map<String, dynamic> map) {
    return NutritionFacts(
      calories: map['calories']?.toDouble() ?? 0,
      protein: map['protein']?.toDouble() ?? 0,
      carbs: map['carbs']?.toDouble() ?? 0,
      fat: map['fat']?.toDouble() ?? 0,
      fiber: map['fiber']?.toDouble() ?? 0,
      sugar: map['sugar']?.toDouble() ?? 0,
      sodium: map['sodium']?.toDouble() ?? 0,
      cholesterol: map['cholesterol']?.toDouble() ?? 0,
      vitamins: Map<String, double>.from(map['vitamins'] ?? {}),
      minerals: Map<String, double>.from(map['minerals'] ?? {}),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fat': fat,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'cholesterol': cholesterol,
      'vitamins': vitamins,
      'minerals': minerals,
    };
  }
}

class ServingSize {
  final String name; // e.g., "1 cup", "1 medium apple"
  final double grams;
  final bool isDefault;

  ServingSize({
    required this.name,
    required this.grams,
    this.isDefault = false,
  });

  factory ServingSize.fromMap(Map<String, dynamic> map) {
    return ServingSize(
      name: map['name'] ?? '',
      grams: map['grams']?.toDouble() ?? 0,
      isDefault: map['isDefault'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'grams': grams,
      'isDefault': isDefault,
    };
  }
}
