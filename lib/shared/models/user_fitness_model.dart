import 'package:cloud_firestore/cloud_firestore.dart';

class UserFitness {
  final double cardioLevel;
  final double strengthLevel;
  final double flexibilityLevel;
  final List<FitnessGoal> goals;
  final List<String> exercisesToAvoid;
  final bool hasPersonalizedGuide;
  final String? guideId;

  UserFitness({
    this.cardioLevel = 0.5,
    this.strengthLevel = 0.5,
    this.flexibilityLevel = 0.5,
    this.goals = const [],
    this.exercisesToAvoid = const [],
    this.hasPersonalizedGuide = false,
    this.guideId,
  });

  factory UserFitness.fromMap(Map<String, dynamic> map) {
    return UserFitness(
      cardioLevel: map['cardioLevel']?.toDouble() ?? 0.5,
      strengthLevel: map['strengthLevel']?.toDouble() ?? 0.5,
      flexibilityLevel: map['flexibilityLevel']?.toDouble() ?? 0.5,
      goals: (map['goals'] as List<dynamic>?)
              ?.map((g) => FitnessGoal.fromMap(g as Map<String, dynamic>))
              .toList() ??
          [],
      exercisesToAvoid: List<String>.from(map['exercisesToAvoid'] ?? []),
      hasPersonalizedGuide: map['hasPersonalizedGuide'] ?? false,
      guideId: map['guideId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'cardioLevel': cardioLevel,
      'strengthLevel': strengthLevel,
      'flexibilityLevel': flexibilityLevel,
      'goals': goals.map((g) => g.toMap()).toList(),
      'exercisesToAvoid': exercisesToAvoid,
      'hasPersonalizedGuide': hasPersonalizedGuide,
      'guideId': guideId,
    };
  }
}

class FitnessGoal {
  final String type;
  final int priority;
  final DateTime selectedAt;
  final String? sportActivity;

  FitnessGoal({
    required this.type,
    required this.priority,
    required this.selectedAt,
    this.sportActivity,
  });

  factory FitnessGoal.fromMap(Map<String, dynamic> map) {
    return FitnessGoal(
      type: map['type'] ?? '',
      priority: map['priority'] ?? 1,
      selectedAt: map['selectedAt'] is Timestamp 
          ? (map['selectedAt'] as Timestamp).toDate()
          : DateTime.fromMillisecondsSinceEpoch(map['selectedAt'] ?? 0),
      sportActivity: map['sportActivity'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'priority': priority,
      'selectedAt': Timestamp.fromDate(selectedAt),
      'sportActivity': sportActivity,
    };
  }
}

enum FitnessGoalType {
  healthOptimization,
  muscleGain,
  weightLoss,
  generalFitness,
  endurance,
  strength,
  flexibility,
}

enum ActivityLevel {
  sedentary,
  lightlyActive,
  moderatelyActive,
  veryActive,
  extremelyActive,
}
