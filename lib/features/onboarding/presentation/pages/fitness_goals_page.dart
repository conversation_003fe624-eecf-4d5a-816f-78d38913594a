import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/onboarding_provider.dart';

class FitnessGoalsPage extends ConsumerStatefulWidget {
  const FitnessGoalsPage({super.key});

  @override
  ConsumerState<FitnessGoalsPage> createState() => _FitnessGoalsPageState();
}

class _FitnessGoalsPageState extends ConsumerState<FitnessGoalsPage> {
  String _selectedFitnessLevel = 'beginner';
  String _selectedActivityLevel = 'moderate';
  int _workoutDuration = 30;
  int _workoutsPerWeek = 3;

  final List<String> _fitnessGoals = [
    'Lose Weight',
    'Build Muscle',
    'Improve Endurance',
    'Increase Strength',
    'Stay Active',
    'Improve Flexibility',
    'Sport Performance',
    'General Health',
  ];

  final List<String> _equipment = [
    'No Equipment',
    'Dumbbells',
    'Resistance Bands',
    'Pull-up Bar',
    'Kettlebells',
    'Barbell',
    'Gym Access',
    'Yoga Mat',
  ];

  @override
  void initState() {
    super.initState();
    
    // Initialize with existing data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final data = ref.read(onboardingDataProvider);
      _selectedFitnessLevel = data.fitnessLevel ?? 'beginner';
      _selectedActivityLevel = data.activityLevel ?? 'moderate';
      _workoutDuration = data.workoutDuration ?? 30;
      _workoutsPerWeek = data.workoutsPerWeek ?? 3;
      setState(() {});
    });
  }

  void _updateData() {
    final data = ref.read(onboardingDataProvider);
    ref.read(onboardingDataProvider.notifier).updateFitnessInfo(
      fitnessLevel: _selectedFitnessLevel,
      activityLevel: _selectedActivityLevel,
      workoutDuration: _workoutDuration,
      workoutsPerWeek: _workoutsPerWeek,
      fitnessGoals: data.fitnessGoals,
      availableEquipment: data.availableEquipment,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final data = ref.watch(onboardingDataProvider);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Fitness Goals',
            style: theme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tell us about your fitness goals and experience level.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Fitness Level
                  _buildFitnessLevelSelection(context),
                  const SizedBox(height: 24),
                  
                  // Activity Level
                  _buildActivityLevelSelection(context),
                  const SizedBox(height: 24),
                  
                  // Fitness Goals
                  _buildFitnessGoalsSelection(context, data),
                  const SizedBox(height: 24),
                  
                  // Available Equipment
                  _buildEquipmentSelection(context, data),
                  const SizedBox(height: 24),
                  
                  // Workout Preferences
                  _buildWorkoutPreferences(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFitnessLevelSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    final levels = [
      {'value': 'beginner', 'title': 'Beginner', 'description': 'New to fitness'},
      {'value': 'intermediate', 'title': 'Intermediate', 'description': 'Some experience'},
      {'value': 'advanced', 'title': 'Advanced', 'description': 'Very experienced'},
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fitness Level',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...levels.map((level) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildSelectionTile(
            value: level['value']!,
            title: level['title']!,
            subtitle: level['description']!,
            isSelected: _selectedFitnessLevel == level['value'],
            onTap: () {
              setState(() {
                _selectedFitnessLevel = level['value']!;
              });
              _updateData();
            },
          ),
        )),
      ],
    );
  }

  Widget _buildActivityLevelSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    final levels = [
      {'value': 'sedentary', 'title': 'Sedentary', 'description': 'Little to no exercise'},
      {'value': 'light', 'title': 'Light', 'description': '1-3 days per week'},
      {'value': 'moderate', 'title': 'Moderate', 'description': '3-5 days per week'},
      {'value': 'active', 'title': 'Active', 'description': '6-7 days per week'},
      {'value': 'very_active', 'title': 'Very Active', 'description': 'Very intense exercise'},
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Activity Level',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...levels.map((level) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildSelectionTile(
            value: level['value']!,
            title: level['title']!,
            subtitle: level['description']!,
            isSelected: _selectedActivityLevel == level['value'],
            onTap: () {
              setState(() {
                _selectedActivityLevel = level['value']!;
              });
              _updateData();
            },
          ),
        )),
      ],
    );
  }

  Widget _buildFitnessGoalsSelection(BuildContext context, dynamic data) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fitness Goals (Select all that apply)',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _fitnessGoals.map((goal) {
            final isSelected = data.fitnessGoals.contains(goal);
            return FilterChip(
              label: Text(goal),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  ref.read(onboardingDataProvider.notifier).addFitnessGoal(goal);
                } else {
                  ref.read(onboardingDataProvider.notifier).removeFitnessGoal(goal);
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildEquipmentSelection(BuildContext context, dynamic data) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Equipment (Select all that apply)',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _equipment.map((equipment) {
            final isSelected = data.availableEquipment.contains(equipment);
            return FilterChip(
              label: Text(equipment),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  ref.read(onboardingDataProvider.notifier).addEquipment(equipment);
                } else {
                  ref.read(onboardingDataProvider.notifier).removeEquipment(equipment);
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildWorkoutPreferences(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Preferences',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // Workout Duration
        Text(
          'Preferred Workout Duration: $_workoutDuration minutes',
          style: theme.textTheme.bodyMedium,
        ),
        Slider(
          value: _workoutDuration.toDouble(),
          min: 15,
          max: 90,
          divisions: 15,
          label: '$_workoutDuration min',
          onChanged: (value) {
            setState(() {
              _workoutDuration = value.round();
            });
            _updateData();
          },
        ),
        
        const SizedBox(height: 16),
        
        // Workouts Per Week
        Text(
          'Workouts Per Week: $_workoutsPerWeek',
          style: theme.textTheme.bodyMedium,
        ),
        Slider(
          value: _workoutsPerWeek.toDouble(),
          min: 1,
          max: 7,
          divisions: 6,
          label: '$_workoutsPerWeek days',
          onChanged: (value) {
            setState(() {
              _workoutsPerWeek = value.round();
            });
            _updateData();
          },
        ),
      ],
    );
  }

  Widget _buildSelectionTile({
    required String value,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: isSelected ? value : '',
              onChanged: (_) => onTap(),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: isSelected ? theme.colorScheme.primary : null,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
