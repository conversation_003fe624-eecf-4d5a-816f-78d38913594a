import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/onboarding_provider.dart';

class PreferencesPage extends ConsumerStatefulWidget {
  const PreferencesPage({super.key});

  @override
  ConsumerState<PreferencesPage> createState() => _PreferencesPageState();
}

class _PreferencesPageState extends ConsumerState<PreferencesPage> {
  String _selectedTheme = 'system';
  String _selectedLanguage = 'en';
  String _selectedUnits = 'metric';
  bool _notifications = true;

  final List<String> _workoutEnvironments = [
    'Home (No Equipment)',
    'Home (With Equipment)',
    'Gym',
    'Outdoor',
    'Pool',
  ];

  @override
  void initState() {
    super.initState();
    
    // Initialize with existing data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final data = ref.read(onboardingDataProvider);
      _selectedTheme = data.theme ?? 'system';
      _selectedLanguage = data.language ?? 'en';
      _selectedUnits = data.units ?? 'metric';
      _notifications = data.notifications;
      setState(() {});
    });
  }

  void _updateData() {
    final data = ref.read(onboardingDataProvider);
    ref.read(onboardingDataProvider.notifier).updatePreferences(
      theme: _selectedTheme,
      language: _selectedLanguage,
      units: _selectedUnits,
      notifications: _notifications,
      workoutEnvironments: data.workoutEnvironments,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final data = ref.watch(onboardingDataProvider);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Preferences',
            style: theme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Customize your app experience and workout preferences.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Workout Environments
                  _buildWorkoutEnvironments(context, data),
                  const SizedBox(height: 24),
                  
                  // App Theme
                  _buildThemeSelection(context),
                  const SizedBox(height: 24),
                  
                  // Units
                  _buildUnitsSelection(context),
                  const SizedBox(height: 24),
                  
                  // Language
                  _buildLanguageSelection(context),
                  const SizedBox(height: 24),
                  
                  // Notifications
                  _buildNotificationSettings(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutEnvironments(BuildContext context, dynamic data) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preferred Workout Environments',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Select where you prefer to work out (you can choose multiple)',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),
        
        ..._workoutEnvironments.map((environment) {
          final environmentKey = environment.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
          final isSelected = data.workoutEnvironments.contains(environmentKey);
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildEnvironmentTile(
              environment: environment,
              environmentKey: environmentKey,
              isSelected: isSelected,
              onChanged: (selected) {
                if (selected) {
                  ref.read(onboardingDataProvider.notifier).addWorkoutEnvironment(environmentKey);
                } else {
                  ref.read(onboardingDataProvider.notifier).removeWorkoutEnvironment(environmentKey);
                }
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildEnvironmentTile({
    required String environment,
    required String environmentKey,
    required bool isSelected,
    required Function(bool) onChanged,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: () => onChanged(!isSelected),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Checkbox(
              value: isSelected,
              onChanged: (value) => onChanged(value ?? false),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                environment,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: isSelected ? theme.colorScheme.primary : null,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            Icon(
              _getEnvironmentIcon(environmentKey),
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getEnvironmentIcon(String environment) {
    switch (environment) {
      case 'homenoequipment':
        return Icons.home;
      case 'homewithequipment':
        return Icons.home_filled;
      case 'gym':
        return Icons.fitness_center;
      case 'outdoor':
        return Icons.park;
      case 'pool':
        return Icons.pool;
      default:
        return Icons.place;
    }
  }

  Widget _buildThemeSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    final themes = [
      {'value': 'system', 'title': 'System', 'icon': Icons.brightness_auto},
      {'value': 'light', 'title': 'Light', 'icon': Icons.brightness_high},
      {'value': 'dark', 'title': 'Dark', 'icon': Icons.brightness_2},
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'App Theme',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: themes.map((themeOption) => Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 8),
              child: _buildThemeOption(
                value: themeOption['value'] as String,
                title: themeOption['title'] as String,
                icon: themeOption['icon'] as IconData,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildThemeOption({
    required String value,
    required String title,
    required IconData icon,
  }) {
    final theme = Theme.of(context);
    final isSelected = _selectedTheme == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedTheme = value;
        });
        _updateData();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitsSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Measurement Units',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildUnitsOption('metric', 'Metric', 'kg, cm, km'),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildUnitsOption('imperial', 'Imperial', 'lbs, ft, miles'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUnitsOption(String value, String title, String subtitle) {
    final theme = Theme.of(context);
    final isSelected = _selectedUnits == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedUnits = value;
        });
        _updateData();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    final languages = [
      {'value': 'en', 'title': 'English'},
      {'value': 'es', 'title': 'Español'},
      {'value': 'fr', 'title': 'Français'},
      {'value': 'de', 'title': 'Deutsch'},
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Language',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<String>(
          value: _selectedLanguage,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: languages.map((language) => DropdownMenuItem(
            value: language['value'],
            child: Text(language['title']!),
          )).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedLanguage = value;
              });
              _updateData();
            }
          },
        ),
      ],
    );
  }

  Widget _buildNotificationSettings(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notifications',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        SwitchListTile(
          title: const Text('Enable Notifications'),
          subtitle: const Text('Get reminders for workouts and progress updates'),
          value: _notifications,
          onChanged: (value) {
            setState(() {
              _notifications = value;
            });
            _updateData();
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
}
