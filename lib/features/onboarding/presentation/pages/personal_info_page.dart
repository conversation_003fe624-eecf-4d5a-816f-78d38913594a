import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/onboarding_provider.dart';
import '../../../../shared/widgets/forms/custom_text_field.dart';

class PersonalInfoPage extends ConsumerStatefulWidget {
  const PersonalInfoPage({super.key});

  @override
  ConsumerState<PersonalInfoPage> createState() => _PersonalInfoPageState();
}

class _PersonalInfoPageState extends ConsumerState<PersonalInfoPage> {
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  DateTime? _selectedDate;
  String _selectedGender = 'male';
  String _selectedUnits = 'metric';

  @override
  void initState() {
    super.initState();
    
    // Initialize with existing data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final data = ref.read(onboardingDataProvider);
      _heightController.text = data.height?.toString() ?? '';
      _weightController.text = data.weight?.toString() ?? '';
      _selectedDate = data.dateOfBirth;
      _selectedGender = data.gender ?? 'male';
      _selectedUnits = data.preferredUnits ?? 'metric';
      setState(() {});
    });
  }

  @override
  void dispose() {
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _updateData() {
    ref.read(onboardingDataProvider.notifier).updatePersonalInfo(
      dateOfBirth: _selectedDate,
      gender: _selectedGender,
      height: double.tryParse(_heightController.text),
      weight: double.tryParse(_weightController.text),
      preferredUnits: _selectedUnits,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Personal Information',
            style: theme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tell us about yourself so we can create personalized recommendations.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: 32),
          
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date of Birth
                  _buildDateOfBirthField(context),
                  const SizedBox(height: 24),
                  
                  // Gender Selection
                  _buildGenderSelection(context),
                  const SizedBox(height: 24),
                  
                  // Units Selection
                  _buildUnitsSelection(context),
                  const SizedBox(height: 24),
                  
                  // Height and Weight
                  Row(
                    children: [
                      Expanded(
                        child: _buildHeightField(context),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildWeightField(context),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // BMI Display
                  _buildBMIDisplay(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateOfBirthField(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date of Birth',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: _selectedDate ?? DateTime.now().subtract(const Duration(days: 365 * 25)),
              firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)),
              lastDate: DateTime.now().subtract(const Duration(days: 365 * 13)),
            );
            if (date != null) {
              setState(() {
                _selectedDate = date;
              });
              _updateData();
            }
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Text(
                  _selectedDate != null
                      ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                      : 'Select your date of birth',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: _selectedDate != null
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenderSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildGenderOption('male', 'Male', Icons.male),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildGenderOption('female', 'Female', Icons.female),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildGenderOption('other', 'Other', Icons.person),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGenderOption(String value, String label, IconData icon) {
    final theme = Theme.of(context);
    final isSelected = _selectedGender == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
        _updateData();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitsSelection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preferred Units',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildUnitsOption('metric', 'Metric (kg, cm)'),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildUnitsOption('imperial', 'Imperial (lbs, ft)'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUnitsOption(String value, String label) {
    final theme = Theme.of(context);
    final isSelected = _selectedUnits == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedUnits = value;
        });
        _updateData();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primaryContainer : null,
          border: Border.all(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurfaceVariant,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildHeightField(BuildContext context) {
    return CustomTextField(
      controller: _heightController,
      label: _selectedUnits == 'metric' ? 'Height (cm)' : 'Height (ft)',
      keyboardType: TextInputType.number,
      onChanged: (value) => _updateData(),
      prefixIcon: Icons.height,
    );
  }

  Widget _buildWeightField(BuildContext context) {
    return CustomTextField(
      controller: _weightController,
      label: _selectedUnits == 'metric' ? 'Weight (kg)' : 'Weight (lbs)',
      keyboardType: TextInputType.number,
      onChanged: (value) => _updateData(),
      prefixIcon: Icons.monitor_weight,
    );
  }

  Widget _buildBMIDisplay(BuildContext context) {
    final theme = Theme.of(context);
    final data = ref.watch(onboardingDataProvider);
    final bmi = data.bmi;
    final bmiCategory = data.bmiCategory;
    
    if (bmi == null) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.analytics,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'BMI: ${bmi.toStringAsFixed(1)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (bmiCategory != null)
                  Text(
                    bmiCategory,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
