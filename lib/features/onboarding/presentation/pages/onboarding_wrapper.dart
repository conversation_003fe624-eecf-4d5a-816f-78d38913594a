import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/onboarding_provider.dart';
import '../../../../shared/models/onboarding_step_model.dart';
import '../../../home/<USER>/pages/home_page.dart';
import 'welcome_page.dart';
import 'personal_info_page.dart';
import 'fitness_goals_page.dart';
import 'preferences_page.dart';
import 'completion_page.dart';

class OnboardingWrapper extends ConsumerWidget {
  const OnboardingWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentStepIndex = ref.watch(currentOnboardingStepProvider);
    final steps = ref.watch(onboardingStepsProvider);
    
    // Check if onboarding is completed
    final onboardingCompletion = ref.watch(onboardingCompletionProvider);
    
    return onboardingCompletion.when(
      data: (isCompleted) {
        if (isCompleted) {
          return const HomePage();
        }
        
        return Scaffold(
          body: SafeArea(
            child: Column(
              children: [
                // Progress indicator
                _buildProgressIndicator(context, currentStepIndex, steps.length),
                
                // Current step content
                Expanded(
                  child: _buildCurrentStep(context, ref, currentStepIndex, steps),
                ),
                
                // Navigation buttons
                _buildNavigationButtons(context, ref, currentStepIndex, steps),
              ],
            ),
          ),
        );
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading onboarding',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(onboardingCompletionProvider);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(BuildContext context, int currentStep, int totalSteps) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Step ${currentStep + 1} of $totalSteps',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const Spacer(),
              Text(
                '${((currentStep + 1) / totalSteps * 100).round()}%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (currentStep + 1) / totalSteps,
            backgroundColor: theme.colorScheme.surfaceVariant,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentStep(BuildContext context, WidgetRef ref, int currentStepIndex, List<OnboardingStep> steps) {
    if (currentStepIndex >= steps.length) {
      return const Center(child: Text('Invalid step'));
    }

    final currentStep = steps[currentStepIndex];
    
    switch (currentStep.type) {
      case OnboardingStepType.welcome:
        return const WelcomePage();
      case OnboardingStepType.personalInfo:
        return const PersonalInfoPage();
      case OnboardingStepType.fitnessGoals:
        return const FitnessGoalsPage();
      case OnboardingStepType.preferences:
        return const PreferencesPage();
      case OnboardingStepType.completion:
        return const CompletionPage();
    }
  }

  Widget _buildNavigationButtons(BuildContext context, WidgetRef ref, int currentStepIndex, List<OnboardingStep> steps) {
    final theme = Theme.of(context);
    final controller = ref.read(onboardingControllerProvider);
    final onboardingData = ref.watch(onboardingDataProvider);
    
    final isFirstStep = currentStepIndex == 0;
    final isLastStep = currentStepIndex == steps.length - 1;
    final currentStep = steps[currentStepIndex];
    
    // Check if current step can be completed
    bool canProceed = false;
    switch (currentStep.type) {
      case OnboardingStepType.welcome:
        canProceed = true;
        break;
      case OnboardingStepType.personalInfo:
        canProceed = onboardingData.isPersonalInfoComplete;
        break;
      case OnboardingStepType.fitnessGoals:
        canProceed = onboardingData.isFitnessInfoComplete;
        break;
      case OnboardingStepType.preferences:
        canProceed = onboardingData.isPreferencesComplete;
        break;
      case OnboardingStepType.completion:
        canProceed = true;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Back button
          if (!isFirstStep)
            Expanded(
              child: OutlinedButton(
                onPressed: () => controller.previousStep(),
                child: const Text('Back'),
              ),
            ),
          
          if (!isFirstStep) const SizedBox(width: 16),
          
          // Next/Complete button
          Expanded(
            flex: isFirstStep ? 1 : 1,
            child: ElevatedButton(
              onPressed: canProceed ? () async {
                if (isLastStep) {
                  // Complete onboarding
                  try {
                    await controller.completeOnboarding();
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error completing onboarding: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } else {
                  // Save current step data and proceed
                  await _saveCurrentStepData(ref, currentStep.type, onboardingData);
                  controller.nextStep();
                }
              } : null,
              child: Text(isLastStep ? 'Complete' : 'Next'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveCurrentStepData(WidgetRef ref, OnboardingStepType stepType, dynamic data) async {
    final controller = ref.read(onboardingControllerProvider);
    
    try {
      switch (stepType) {
        case OnboardingStepType.personalInfo:
          await controller.updateStep('personalInfo', {
            'dateOfBirth': data.dateOfBirth?.toIso8601String(),
            'gender': data.gender,
            'height': data.height,
            'weight': data.weight,
            'preferredUnits': data.preferredUnits,
          });
          break;
        case OnboardingStepType.fitnessGoals:
          await controller.updateStep('fitnessGoals', {
            'fitnessLevel': data.fitnessLevel,
            'fitnessGoals': data.fitnessGoals,
            'targetMuscleGroups': data.targetMuscleGroups,
            'activityLevel': data.activityLevel,
            'availableEquipment': data.availableEquipment,
            'preferredWorkoutTypes': data.preferredWorkoutTypes,
            'workoutDuration': data.workoutDuration,
            'workoutsPerWeek': data.workoutsPerWeek,
          });
          break;
        case OnboardingStepType.preferences:
          await controller.updateStep('preferences', {
            'workoutEnvironments': data.workoutEnvironments,
            'theme': data.theme,
            'notifications': data.notifications,
            'language': data.language,
            'units': data.units,
          });
          break;
        default:
          break;
      }
    } catch (e) {
      // Handle error silently or show a message
      debugPrint('Error saving step data: $e');
    }
  }
}
