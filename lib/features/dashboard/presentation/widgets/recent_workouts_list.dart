import 'package:flutter/material.dart';
import '../../../../shared/widgets/cards/workout_card.dart';

class RecentWorkoutsList extends StatelessWidget {
  const RecentWorkoutsList({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 160,
      child: <PERSON>View(
        scrollDirection: Axis.horizontal,
        children: const [
          WorkoutCard(
            title: 'Running',
            duration: '32 min',
            icon: Icons.directions_run,
          ),
          WorkoutCard(
            title: 'Strength',
            duration: '45 min',
            icon: Icons.fitness_center,
          ),
          WorkoutCard(
            title: 'Yoga',
            duration: '20 min',
            icon: Icons.self_improvement,
          ),
          WorkoutCard(
            title: 'Cycling',
            duration: '60 min',
            icon: Icons.directions_bike,
          ),
        ],
      ),
    );
  }
}
