import 'package:flutter/material.dart';
import '../../../../shared/widgets/progress/nutrient_progress.dart';

class NutritionSummaryCard extends StatelessWidget {
  const NutritionSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Calories',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Text('1,450 / 2,000 kcal'),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: 0.725,
              borderRadius: BorderRadius.circular(4),
              minHeight: 8,
            ),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                NutrientProgress(
                  label: 'Protein',
                  value: 0.8,
                  amount: '96g',
                ),
                NutrientProgress(
                  label: 'Carbs',
                  value: 0.6,
                  amount: '150g',
                ),
                NutrientProgress(
                  label: 'Fat',
                  value: 0.4,
                  amount: '45g',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
