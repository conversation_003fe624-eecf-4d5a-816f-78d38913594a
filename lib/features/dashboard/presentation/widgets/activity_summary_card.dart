import 'package:flutter/material.dart';
import '../../../../shared/widgets/cards/metric_card.dart';

class ActivitySummaryCard extends StatelessWidget {
  const ActivitySummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Activity',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                MetricCard(
                  icon: Icons.directions_run,
                  value: '2,354',
                  label: 'Steps',
                ),
                Metric<PERSON>ard(
                  icon: Icons.local_fire_department_outlined,
                  value: '325',
                  label: 'Calories',
                ),
                Metric<PERSON>ard(
                  icon: Icons.access_time,
                  value: '32',
                  label: 'Minutes',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
