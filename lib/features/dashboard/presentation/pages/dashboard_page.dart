import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/auth_provider.dart';
import '../widgets/activity_summary_card.dart';
import '../widgets/recent_workouts_list.dart';
import '../widgets/nutrition_summary_card.dart';

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentUserProvider);

    return SafeArea(
      child: CustomScrollView(
        slivers: [
          SliverAppBar(
            floating: true,
            title: const Text('OpenFit'),
            centerTitle: false,
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () async {
                  final authController = ref.read(authControllerProvider);
                  await authController.signOut();
                },
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back${currentUser?.displayName != null ? ', ${currentUser!.displayName}' : ''}!',
                    style: theme.textTheme.displayMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Track your fitness journey',
                    style: theme.textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 24),
                  const ActivitySummaryCard(),
                  const SizedBox(height: 16),
                  Text(
                    'Recent Workouts',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const RecentWorkoutsList(),
                  const SizedBox(height: 16),
                  Text(
                    'Nutrition Today',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  const NutritionSummaryCard(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
