import 'package:flutter/material.dart';
import '../../../../shared/models/user_model.dart';

class FitnessProfileCard extends StatelessWidget {
  final UserModel user;

  const FitnessProfileCard({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.fitness_center,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Fitness Profile',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Fitness Levels
            _buildFitnessLevels(context),
            
            const SizedBox(height: 16),
            
            // Fitness Goals
            if (user.fitness.goals.isNotEmpty) ...[
              _buildSectionTitle(context, 'Fitness Goals'),
              const SizedBox(height: 8),
              _buildGoalsList(context),
              const SizedBox(height: 16),
            ],
            
            // Workout Preferences
            _buildWorkoutPreferences(context),
            
            // Exercises to Avoid
            if (user.fitness.exercisesToAvoid.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSectionTitle(context, 'Exercises to Avoid'),
              const SizedBox(height: 8),
              _buildExercisesToAvoid(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFitnessLevels(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, 'Fitness Levels'),
        const SizedBox(height: 12),
        
        _buildLevelIndicator(
          context,
          'Cardio',
          user.fitness.cardioLevel,
          Icons.favorite,
          Colors.red,
        ),
        
        const SizedBox(height: 8),
        
        _buildLevelIndicator(
          context,
          'Strength',
          user.fitness.strengthLevel,
          Icons.fitness_center,
          Colors.blue,
        ),
        
        const SizedBox(height: 8),
        
        _buildLevelIndicator(
          context,
          'Flexibility',
          user.fitness.flexibilityLevel,
          Icons.self_improvement,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildLevelIndicator(BuildContext context, String label, double level, IconData icon, Color color) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${(level * 100).toInt()}%',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              LinearProgressIndicator(
                value: level,
                backgroundColor: theme.colorScheme.surfaceVariant,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsList(BuildContext context) {
    final theme = Theme.of(context);
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: user.fitness.goals.map((goal) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withOpacity(0.5),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: theme.colorScheme.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getGoalIcon(goal.type),
                size: 16,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                _formatGoalType(goal.type),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildWorkoutPreferences(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, 'Workout Preferences'),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildPreferenceItem(
                context,
                'Frequency',
                '${user.preferences.workoutsPerWeek}x/week',
                Icons.calendar_today,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPreferenceItem(
                context,
                'Duration',
                '${user.preferences.durationMinutes} min',
                Icons.timer,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Workout Environments
        if (user.preferences.environments.isNotEmpty) ...[
          Text(
            'Preferred Environments',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: user.preferences.environments.map((env) {
              return Chip(
                label: Text(_formatEnvironment(env)),
                backgroundColor: theme.colorScheme.secondaryContainer.withOpacity(0.5),
                side: BorderSide(
                  color: theme.colorScheme.secondary.withOpacity(0.3),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildPreferenceItem(BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.onSurfaceVariant,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExercisesToAvoid(BuildContext context) {
    final theme = Theme.of(context);
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: user.fitness.exercisesToAvoid.map((exercise) {
        return Chip(
          label: Text(exercise),
          backgroundColor: Colors.red.withOpacity(0.1),
          side: BorderSide(
            color: Colors.red.withOpacity(0.3),
          ),
          avatar: const Icon(
            Icons.warning,
            size: 16,
            color: Colors.red,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);
    
    return Text(
      title,
      style: theme.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  IconData _getGoalIcon(String goalType) {
    switch (goalType.toLowerCase()) {
      case 'weightloss':
        return Icons.trending_down;
      case 'musclegain':
        return Icons.fitness_center;
      case 'endurance':
        return Icons.directions_run;
      case 'strength':
        return Icons.sports_gymnastics;
      case 'flexibility':
        return Icons.self_improvement;
      case 'healthoptimization':
        return Icons.health_and_safety;
      default:
        return Icons.flag;
    }
  }

  String _formatGoalType(String goalType) {
    switch (goalType.toLowerCase()) {
      case 'weightloss':
        return 'Weight Loss';
      case 'musclegain':
        return 'Muscle Gain';
      case 'endurance':
        return 'Endurance';
      case 'strength':
        return 'Strength';
      case 'flexibility':
        return 'Flexibility';
      case 'healthoptimization':
        return 'Health Optimization';
      case 'generalfitness':
        return 'General Fitness';
      default:
        return goalType;
    }
  }

  String _formatEnvironment(String env) {
    switch (env.toLowerCase()) {
      case 'homenoequipment':
        return 'Home (No Equipment)';
      case 'homewithequipment':
        return 'Home (With Equipment)';
      case 'gym':
        return 'Gym';
      case 'largegym':
        return 'Large Gym';
      case 'outdoor':
        return 'Outdoor';
      case 'pool':
        return 'Pool';
      default:
        return env;
    }
  }
}
