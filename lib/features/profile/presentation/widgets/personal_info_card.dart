import 'package:flutter/material.dart';
import '../../../../shared/models/user_model.dart';

class PersonalInfoCard extends StatelessWidget {
  final UserModel user;

  const PersonalInfoCard({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Personal Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Personal Info Items
            _buildInfoRow(
              context,
              'Age',
              user.age?.toString() ?? 'Not set',
              Icons.cake,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              context,
              'Gender',
              _formatGender(user.profile.gender),
              Icons.person_outline,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              context,
              'Height',
              _formatHeight(user.profile.height, user.profile.preferredUnits),
              Icons.height,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              context,
              'Weight',
              _formatWeight(user.profile.weight, user.profile.preferredUnits),
              Icons.monitor_weight,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              context,
              'BMI',
              _formatBMI(user.bmi),
              Icons.analytics,
            ),
            
            const SizedBox(height: 16),
            
            // BMI Category Badge
            if (user.bmi != null) _buildBMICategory(context, user.bmi!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 20,
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBMICategory(BuildContext context, double bmi) {
    final theme = Theme.of(context);
    
    String category;
    Color color;
    
    if (bmi < 18.5) {
      category = 'Underweight';
      color = Colors.blue;
    } else if (bmi < 25) {
      category = 'Normal weight';
      color = Colors.green;
    } else if (bmi < 30) {
      category = 'Overweight';
      color = Colors.orange;
    } else {
      category = 'Obese';
      color = Colors.red;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.health_and_safety,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            category,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatGender(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'other':
        return 'Other';
      default:
        return 'Not specified';
    }
  }

  String _formatHeight(double? height, String units) {
    if (height == null) return 'Not set';
    
    if (units == 'imperial') {
      final feet = height / 30.48;
      final feetInt = feet.floor();
      final inches = ((feet - feetInt) * 12).round();
      return '${feetInt}\'${inches}"';
    } else {
      return '${height.toStringAsFixed(0)} cm';
    }
  }

  String _formatWeight(double? weight, String units) {
    if (weight == null) return 'Not set';
    
    if (units == 'imperial') {
      final pounds = weight * 2.20462;
      return '${pounds.toStringAsFixed(1)} lbs';
    } else {
      return '${weight.toStringAsFixed(1)} kg';
    }
  }

  String _formatBMI(double? bmi) {
    if (bmi == null) return 'Not calculated';
    return bmi.toStringAsFixed(1);
  }
}
