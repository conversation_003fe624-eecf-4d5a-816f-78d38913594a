import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/auth_provider.dart';
import '../widgets/profile_header.dart';
import '../widgets/profile_stats_card.dart';
import '../widgets/personal_info_card.dart';
import '../widgets/fitness_profile_card.dart';
import '../widgets/app_settings_card.dart';
import '../widgets/account_actions_card.dart';

class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentUser = ref.watch(currentUserProvider);

    if (currentUser == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final userProfileAsync = ref.watch(userProfileProvider(currentUser.uid));

    return Scaffold(
      body: userProfileAsync.when(
        data: (userModel) {
          if (userModel == null) {
            return const Center(
              child: Text('User profile not found'),
            );
          }

          return CustomScrollView(
            slivers: [
              // App Bar with profile header
              SliverAppBar(
                expandedHeight: 160,
                floating: false,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: ProfileHeader(user: userModel),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _showEditProfileDialog(context, ref, userModel),
                  ),
                ],
              ),

              // Profile content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      // Statistics Card
                      ProfileStatsCard(user: userModel),
                      const SizedBox(height: 16),

                      // Personal Information Card
                      PersonalInfoCard(user: userModel),
                      const SizedBox(height: 16),

                      // Fitness Profile Card
                      FitnessProfileCard(user: userModel),
                      const SizedBox(height: 16),

                      // App Settings Card
                      AppSettingsCard(user: userModel),
                      const SizedBox(height: 16),

                      // Account Actions Card
                      AccountActionsCard(user: userModel),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading profile',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(userProfileProvider(currentUser.uid));
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context, WidgetRef ref, dynamic userModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text('Profile editing feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
