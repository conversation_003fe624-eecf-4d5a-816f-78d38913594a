import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../shared/models/onboarding_step_model.dart';
import '../../shared/models/onboarding_data_model.dart';
import '../services/onboarding_service.dart';
import '../services/firebase_service.dart';

// Onboarding data state provider
final onboardingDataProvider = StateNotifierProvider<OnboardingDataNotifier, OnboardingData>((ref) {
  return OnboardingDataNotifier();
});

// Current onboarding step provider
final currentOnboardingStepProvider = StateProvider<int>((ref) => 0);

// Onboarding steps provider
final onboardingStepsProvider = StateProvider<List<OnboardingStep>>((ref) {
  return OnboardingFlow.getDefaultSteps();
});

// Onboarding completion status provider
final onboardingCompletionProvider = FutureProvider<bool>((ref) async {
  final userId = FirebaseService.currentUserId;
  if (userId == null) return false;
  
  return await OnboardingService.hasCompletedOnboarding(userId);
});

// Onboarding progress provider
final onboardingProgressProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final userId = FirebaseService.currentUserId;
  if (userId == null) return {};
  
  return await OnboardingService.getOnboardingProgress(userId);
});

class OnboardingDataNotifier extends StateNotifier<OnboardingData> {
  OnboardingDataNotifier() : super(OnboardingData());

  // Personal Information Updates
  void updatePersonalInfo({
    DateTime? dateOfBirth,
    String? gender,
    double? height,
    double? weight,
    String? preferredUnits,
  }) {
    state = state.copyWith(
      dateOfBirth: dateOfBirth,
      gender: gender,
      height: height,
      weight: weight,
      preferredUnits: preferredUnits,
    );
  }

  // Fitness Information Updates
  void updateFitnessInfo({
    String? fitnessLevel,
    List<String>? fitnessGoals,
    List<String>? targetMuscleGroups,
    String? activityLevel,
    List<String>? availableEquipment,
    List<String>? preferredWorkoutTypes,
    int? workoutDuration,
    int? workoutsPerWeek,
  }) {
    state = state.copyWith(
      fitnessLevel: fitnessLevel,
      fitnessGoals: fitnessGoals,
      targetMuscleGroups: targetMuscleGroups,
      activityLevel: activityLevel,
      availableEquipment: availableEquipment,
      preferredWorkoutTypes: preferredWorkoutTypes,
      workoutDuration: workoutDuration,
      workoutsPerWeek: workoutsPerWeek,
    );
  }

  // Preferences Updates
  void updatePreferences({
    List<String>? workoutEnvironments,
    String? theme,
    bool? notifications,
    String? language,
    String? units,
  }) {
    state = state.copyWith(
      workoutEnvironments: workoutEnvironments,
      theme: theme,
      notifications: notifications,
      language: language,
      units: units,
    );
  }

  // Health Information Updates
  void updateHealthInfo({
    List<String>? healthConditions,
    List<String>? injuries,
    bool? hasHealthRestrictions,
  }) {
    state = state.copyWith(
      healthConditions: healthConditions,
      injuries: injuries,
      hasHealthRestrictions: hasHealthRestrictions,
    );
  }

  // Add fitness goal
  void addFitnessGoal(String goal) {
    final currentGoals = List<String>.from(state.fitnessGoals);
    if (!currentGoals.contains(goal)) {
      currentGoals.add(goal);
      state = state.copyWith(fitnessGoals: currentGoals);
    }
  }

  // Remove fitness goal
  void removeFitnessGoal(String goal) {
    final currentGoals = List<String>.from(state.fitnessGoals);
    currentGoals.remove(goal);
    state = state.copyWith(fitnessGoals: currentGoals);
  }

  // Add equipment
  void addEquipment(String equipment) {
    final currentEquipment = List<String>.from(state.availableEquipment);
    if (!currentEquipment.contains(equipment)) {
      currentEquipment.add(equipment);
      state = state.copyWith(availableEquipment: currentEquipment);
    }
  }

  // Remove equipment
  void removeEquipment(String equipment) {
    final currentEquipment = List<String>.from(state.availableEquipment);
    currentEquipment.remove(equipment);
    state = state.copyWith(availableEquipment: currentEquipment);
  }

  // Add workout environment
  void addWorkoutEnvironment(String environment) {
    final currentEnvironments = List<String>.from(state.workoutEnvironments);
    if (!currentEnvironments.contains(environment)) {
      currentEnvironments.add(environment);
      state = state.copyWith(workoutEnvironments: currentEnvironments);
    }
  }

  // Remove workout environment
  void removeWorkoutEnvironment(String environment) {
    final currentEnvironments = List<String>.from(state.workoutEnvironments);
    currentEnvironments.remove(environment);
    state = state.copyWith(workoutEnvironments: currentEnvironments);
  }

  // Reset onboarding data
  void reset() {
    state = OnboardingData();
  }

  // Load data from map (useful for restoring state)
  void loadFromMap(Map<String, dynamic> data) {
    state = OnboardingData.fromMap(data);
  }
}

// Onboarding controller provider
final onboardingControllerProvider = Provider<OnboardingController>((ref) {
  return OnboardingController(ref);
});

class OnboardingController {
  final Ref _ref;

  OnboardingController(this._ref);

  // Complete onboarding process
  Future<void> completeOnboarding() async {
    final userId = FirebaseService.currentUserId;
    if (userId == null) throw Exception('User not authenticated');

    final onboardingData = _ref.read(onboardingDataProvider);
    
    if (!onboardingData.isComplete) {
      throw Exception('Onboarding data is incomplete');
    }

    await OnboardingService.completeOnboarding(
      userId: userId,
      profile: onboardingData.toUserProfile(),
      fitness: onboardingData.toUserFitness(),
      preferences: onboardingData.toUserPreferences(),
    );

    // Refresh the completion status
    _ref.invalidate(onboardingCompletionProvider);
  }

  // Update onboarding step
  Future<void> updateStep(String stepName, Map<String, dynamic> stepData) async {
    final userId = FirebaseService.currentUserId;
    if (userId == null) throw Exception('User not authenticated');

    await OnboardingService.updateOnboardingStep(
      userId: userId,
      stepName: stepName,
      stepData: stepData,
    );

    // Refresh the progress
    _ref.invalidate(onboardingProgressProvider);
  }

  // Navigate to next step
  void nextStep() {
    final currentStep = _ref.read(currentOnboardingStepProvider);
    final steps = _ref.read(onboardingStepsProvider);
    
    if (currentStep < steps.length - 1) {
      _ref.read(currentOnboardingStepProvider.notifier).state = currentStep + 1;
    }
  }

  // Navigate to previous step
  void previousStep() {
    final currentStep = _ref.read(currentOnboardingStepProvider);
    
    if (currentStep > 0) {
      _ref.read(currentOnboardingStepProvider.notifier).state = currentStep - 1;
    }
  }

  // Go to specific step
  void goToStep(int stepIndex) {
    final steps = _ref.read(onboardingStepsProvider);
    
    if (stepIndex >= 0 && stepIndex < steps.length) {
      _ref.read(currentOnboardingStepProvider.notifier).state = stepIndex;
    }
  }

  // Reset onboarding (useful for testing)
  Future<void> resetOnboarding() async {
    final userId = FirebaseService.currentUserId;
    if (userId == null) throw Exception('User not authenticated');

    await OnboardingService.resetOnboarding(userId);
    
    // Reset local state
    _ref.read(onboardingDataProvider.notifier).reset();
    _ref.read(currentOnboardingStepProvider.notifier).state = 0;
    
    // Refresh providers
    _ref.invalidate(onboardingCompletionProvider);
    _ref.invalidate(onboardingProgressProvider);
  }
}
