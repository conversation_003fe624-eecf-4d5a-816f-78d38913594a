import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/user_fitness_model.dart';
import '../../shared/models/user_preferences_model.dart';
import '../../shared/models/user_stats_model.dart';

// Auth state provider
final authStateProvider = StreamProvider<User?>((ref) {
  return AuthService.authStateChanges;
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// User profile provider
final userProfileProvider = FutureProvider.family<UserModel?, String>((ref, uid) async {
  final userData = await AuthService.getUserProfile(uid);
  if (userData == null) return null;

  // Create UserModel with the new structure
  return UserModel(
    uid: userData['uid'] ?? uid,
    email: userData['email'] ?? '',
    displayName: userData['displayName'] ?? userData['name'] ?? '',
    photoURL: userData['photoURL'],
    profile: UserProfile.fromMap(userData['profile'] ?? {}),
    fitness: UserFitness.fromMap(userData['fitness'] ?? {}),
    preferences: UserPreferences.fromMap(userData['preferences'] ?? {}),
    stats: UserStats.fromMap(userData['stats'] ?? {}),
    onboardingCompleted: userData['onboardingCompleted'] ?? false,
    onboardingCompletedAt: userData['onboardingCompletedAt']?.toDate(),
    createdAt: userData['createdAt'] ?? 0,
    updatedAt: userData['updatedAt']?.toDate() ?? DateTime.now(),
    lastUpdated: userData['lastUpdated']?.toDate() ?? DateTime.now(),
  );
});

// Auth controller
final authControllerProvider = Provider<AuthController>((ref) {
  return AuthController(ref);
});

class AuthController {
  final Ref _ref;

  AuthController(this._ref);

  Future<void> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      await AuthService.signUpWithEmail(
        email: email,
        password: password,
        name: name,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    try {
      await AuthService.signInWithEmail(
        email: email,
        password: password,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> signOut() async {
    try {
      await AuthService.signOut();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await AuthService.resetPassword(email);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateProfile(String uid, Map<String, dynamic> data) async {
    try {
      await AuthService.updateUserProfile(uid, data);
      // Invalidate the user profile provider to refresh data
      _ref.invalidate(userProfileProvider(uid));
    } catch (e) {
      rethrow;
    }
  }
}
