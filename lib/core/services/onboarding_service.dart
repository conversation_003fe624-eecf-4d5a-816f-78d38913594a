import 'package:cloud_firestore/cloud_firestore.dart';
import '../../shared/models/user_model.dart';
import '../../shared/models/user_fitness_model.dart';
import '../../shared/models/user_preferences_model.dart';
import '../../shared/models/user_stats_model.dart';
import 'firebase_service.dart';

class OnboardingService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  /// Complete the onboarding process for a user
  static Future<void> completeOnboarding({
    required String userId,
    required UserProfile profile,
    required UserFitness fitness,
    required UserPreferences preferences,
  }) async {
    try {
      final userRef = _firestore.collection('users').doc(userId);
      
      // Update user document with onboarding data
      await userRef.update({
        'profile': profile.toMap(),
        'fitness': fitness.toMap(),
        'preferences': preferences.copyWith(
          onboardingComplete: true,
          fitnessGoalsSet: true,
          comprehensiveOnboardingComplete: true,
        ).toMap(),
        'onboardingCompleted': true,
        'onboardingCompletedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Create initial user stats
      await userRef.update({
        'stats': UserStats().toMap(),
      });

      // Log onboarding completion for analytics
      await _logOnboardingCompletion(userId);
    } catch (e) {
      throw Exception('Failed to complete onboarding: $e');
    }
  }

  /// Update onboarding step progress
  static Future<void> updateOnboardingStep({
    required String userId,
    required String stepName,
    required Map<String, dynamic> stepData,
  }) async {
    try {
      final userRef = _firestore.collection('users').doc(userId);
      
      await userRef.update({
        'onboardingSteps.$stepName': {
          'completed': true,
          'completedAt': FieldValue.serverTimestamp(),
          'data': stepData,
        },
        'updatedAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update onboarding step: $e');
    }
  }

  /// Check if user has completed onboarding
  static Future<bool> hasCompletedOnboarding(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;
      
      final data = userDoc.data() as Map<String, dynamic>;
      return data['onboardingCompleted'] ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get onboarding progress for a user
  static Future<Map<String, dynamic>> getOnboardingProgress(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return {};
      
      final data = userDoc.data() as Map<String, dynamic>;
      return data['onboardingSteps'] ?? {};
    } catch (e) {
      return {};
    }
  }

  /// Create initial user profile after signup
  static Future<void> createInitialUserProfile({
    required String userId,
    required String email,
    required String displayName,
  }) async {
    try {
      final userRef = _firestore.collection('users').doc(userId);
      
      await userRef.set({
        'uid': userId,
        'email': email,
        'displayName': displayName,
        'photoURL': null,
        'profile': UserProfile().toMap(),
        'fitness': UserFitness().toMap(),
        'preferences': UserPreferences().toMap(),
        'stats': UserStats().toMap(),
        'onboardingCompleted': false,
        'onboardingCompletedAt': null,
        'onboardingSteps': {},
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to create initial user profile: $e');
    }
  }

  /// Log onboarding completion for analytics
  static Future<void> _logOnboardingCompletion(String userId) async {
    try {
      // You can add Firebase Analytics logging here
      // FirebaseAnalytics.instance.logEvent(
      //   name: 'onboarding_completed',
      //   parameters: {'user_id': userId},
      // );
    } catch (e) {
      // Silently fail analytics logging
    }
  }

  /// Reset onboarding for a user (useful for testing)
  static Future<void> resetOnboarding(String userId) async {
    try {
      final userRef = _firestore.collection('users').doc(userId);
      
      await userRef.update({
        'onboardingCompleted': false,
        'onboardingCompletedAt': null,
        'onboardingSteps': {},
        'preferences.onboardingComplete': false,
        'preferences.fitnessGoalsSet': false,
        'preferences.comprehensiveOnboardingComplete': false,
        'updatedAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to reset onboarding: $e');
    }
  }
}
