import 'package:intl/intl.dart';

class Formatters {
  // Date formatters
  static final DateFormat _dateFormat = DateFormat('MMM dd, yyyy');
  static final DateFormat _timeFormat = DateFormat('HH:mm');
  static final DateFormat _dateTimeFormat = DateFormat('MMM dd, yyyy HH:mm');
  static final DateFormat _shortDateFormat = DateFormat('MM/dd/yyyy');
  
  // Number formatters
  static final NumberFormat _numberFormat = NumberFormat('#,##0');
  static final NumberFormat _decimalFormat = NumberFormat('#,##0.0');
  static final NumberFormat _currencyFormat = NumberFormat.currency(symbol: '\$');
  static final NumberFormat _percentFormat = NumberFormat.percentPattern();
  
  // Date formatting
  static String formatDate(DateTime date) {
    return _dateFormat.format(date);
  }
  
  static String formatTime(DateTime time) {
    return _timeFormat.format(time);
  }
  
  static String formatDateTime(DateTime dateTime) {
    return _dateTimeFormat.format(dateTime);
  }
  
  static String formatShortDate(DateTime date) {
    return _shortDateFormat.format(date);
  }
  
  // Relative time formatting
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
  
  // Number formatting
  static String formatNumber(num number) {
    return _numberFormat.format(number);
  }
  
  static String formatDecimal(num number) {
    return _decimalFormat.format(number);
  }
  
  static String formatCurrency(num amount) {
    return _currencyFormat.format(amount);
  }
  
  static String formatPercent(num value) {
    return _percentFormat.format(value);
  }
  
  // Duration formatting
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
  
  // File size formatting
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  // Fitness specific formatters
  static String formatCalories(num calories) {
    return '${formatNumber(calories)} cal';
  }
  
  static String formatDistance(num meters) {
    if (meters < 1000) {
      return '${formatNumber(meters)} m';
    } else {
      return '${formatDecimal(meters / 1000)} km';
    }
  }
  
  static String formatWeight(num kg) {
    return '${formatDecimal(kg)} kg';
  }
}
