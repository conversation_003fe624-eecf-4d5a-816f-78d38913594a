// Test script to demonstrate onboarding functionality with Firebase MCP
// This file shows how to use the onboarding system with your Firebase setup

import 'package:cloud_firestore/cloud_firestore.dart';

// Example: Create a test user for onboarding
Future<void> createTestOnboardingUser() async {
  final firestore = FirebaseFirestore.instance;
  
  // Create a test user who hasn't completed onboarding
  await firestore.collection('users').doc('test-onboarding-user').set({
    'uid': 'test-onboarding-user',
    'email': '<EMAIL>',
    'displayName': 'Onboarding Test User',
    'photoURL': null,
    
    // Initial empty profile data
    'profile': {
      'dateOfBirth': null,
      'gender': 'male',
      'height': null,
      'weight': null,
      'preferredUnits': 'metric',
    },
    
    // Initial empty fitness data
    'fitness': {
      'fitnessLevel': null,
      'fitnessGoals': [],
      'targetMuscleGroups': [],
      'activityLevel': null,
      'availableEquipment': [],
      'preferredWorkoutTypes': [],
      'workoutDuration': null,
      'workoutsPerWeek': null,
      'healthConditions': [],
      'injuries': [],
      'hasHealthRestrictions': false,
    },
    
    // Initial preferences
    'preferences': {
      'workoutsPerWeek': 3,
      'durationMinutes': 30,
      'environments': ['homeNoEquipment'],
      'theme': 'system',
      'notifications': true,
      'language': 'en',
      'appSettings': {},
      'units': 'metric',
      'onboardingComplete': false,
      'fitnessGoalsSet': false,
      'comprehensiveOnboardingComplete': false,
    },
    
    // Initial stats
    'stats': {
      'totalWorkouts': 0,
      'totalMinutes': 0,
      'totalCaloriesBurned': 0,
      'currentStreak': 0,
      'longestStreak': 0,
      'weeklyGoal': 150,
      'lastWorkoutDate': null,
      'monthlyStats': {},
    },
    
    // Onboarding status - NOT COMPLETED
    'onboardingCompleted': false,
    'onboardingCompletedAt': null,
    'onboardingSteps': {},
    
    // Timestamps
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('✅ Test onboarding user created successfully!');
}

// Example: Simulate onboarding step completion
Future<void> simulateOnboardingSteps() async {
  final firestore = FirebaseFirestore.instance;
  final userId = 'test-onboarding-user';
  final userRef = firestore.collection('users').doc(userId);
  
  // Step 1: Personal Info
  await userRef.update({
    'onboardingSteps.personalInfo': {
      'completed': true,
      'completedAt': FieldValue.serverTimestamp(),
      'data': {
        'dateOfBirth': '1995-06-15',
        'gender': 'female',
        'height': 165.0,
        'weight': 60.0,
        'preferredUnits': 'metric',
      },
    },
    'profile.dateOfBirth': Timestamp.fromDate(DateTime(1995, 6, 15)),
    'profile.gender': 'female',
    'profile.height': 165.0,
    'profile.weight': 60.0,
    'profile.preferredUnits': 'metric',
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('✅ Personal info step completed');
  
  // Step 2: Fitness Goals
  await userRef.update({
    'onboardingSteps.fitnessGoals': {
      'completed': true,
      'completedAt': FieldValue.serverTimestamp(),
      'data': {
        'fitnessLevel': 'intermediate',
        'fitnessGoals': ['Lose Weight', 'Build Muscle', 'Improve Endurance'],
        'activityLevel': 'moderate',
        'availableEquipment': ['Dumbbells', 'Yoga Mat'],
        'workoutDuration': 45,
        'workoutsPerWeek': 4,
      },
    },
    'fitness.fitnessLevel': 'intermediate',
    'fitness.fitnessGoals': ['Lose Weight', 'Build Muscle', 'Improve Endurance'],
    'fitness.activityLevel': 'moderate',
    'fitness.availableEquipment': ['Dumbbells', 'Yoga Mat'],
    'fitness.workoutDuration': 45,
    'fitness.workoutsPerWeek': 4,
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('✅ Fitness goals step completed');
  
  // Step 3: Preferences
  await userRef.update({
    'onboardingSteps.preferences': {
      'completed': true,
      'completedAt': FieldValue.serverTimestamp(),
      'data': {
        'workoutEnvironments': ['homeWithEquipment', 'gym'],
        'theme': 'dark',
        'notifications': true,
        'language': 'en',
        'units': 'metric',
      },
    },
    'preferences.environments': ['homeWithEquipment', 'gym'],
    'preferences.theme': 'dark',
    'preferences.workoutsPerWeek': 4,
    'preferences.durationMinutes': 45,
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('✅ Preferences step completed');
}

// Example: Complete onboarding
Future<void> completeOnboarding() async {
  final firestore = FirebaseFirestore.instance;
  final userId = 'test-onboarding-user';
  final userRef = firestore.collection('users').doc(userId);
  
  await userRef.update({
    'onboardingCompleted': true,
    'onboardingCompletedAt': FieldValue.serverTimestamp(),
    'preferences.onboardingComplete': true,
    'preferences.fitnessGoalsSet': true,
    'preferences.comprehensiveOnboardingComplete': true,
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('🎉 Onboarding completed successfully!');
}

// Example: Query onboarding status
Future<void> checkOnboardingStatus() async {
  final firestore = FirebaseFirestore.instance;
  
  // Find users who haven't completed onboarding
  final incompleteQuery = await firestore
      .collection('users')
      .where('onboardingCompleted', isEqualTo: false)
      .get();
  
  print('📊 Users with incomplete onboarding: ${incompleteQuery.docs.length}');
  
  for (final doc in incompleteQuery.docs) {
    final data = doc.data();
    print('  - ${data['displayName']} (${data['email']})');
  }
  
  // Find users who completed onboarding
  final completeQuery = await firestore
      .collection('users')
      .where('onboardingCompleted', isEqualTo: true)
      .get();
  
  print('✅ Users with completed onboarding: ${completeQuery.docs.length}');
}

// Example: Reset onboarding for testing
Future<void> resetOnboarding(String userId) async {
  final firestore = FirebaseFirestore.instance;
  final userRef = firestore.collection('users').doc(userId);
  
  await userRef.update({
    'onboardingCompleted': false,
    'onboardingCompletedAt': null,
    'onboardingSteps': {},
    'preferences.onboardingComplete': false,
    'preferences.fitnessGoalsSet': false,
    'preferences.comprehensiveOnboardingComplete': false,
    'updatedAt': FieldValue.serverTimestamp(),
    'lastUpdated': FieldValue.serverTimestamp(),
  });
  
  print('🔄 Onboarding reset for user: $userId');
}

// Main test function
Future<void> runOnboardingTest() async {
  print('🚀 Starting onboarding system test...\n');
  
  try {
    // 1. Create test user
    await createTestOnboardingUser();
    
    // 2. Check initial status
    await checkOnboardingStatus();
    
    // 3. Simulate onboarding steps
    print('\n📝 Simulating onboarding steps...');
    await simulateOnboardingSteps();
    
    // 4. Complete onboarding
    await completeOnboarding();
    
    // 5. Check final status
    print('\n📊 Final status check:');
    await checkOnboardingStatus();
    
    print('\n✅ Onboarding test completed successfully!');
    
  } catch (e) {
    print('❌ Error during onboarding test: $e');
  }
}

/*
To run this test with your Firebase MCP:

1. Use the Firebase MCP to execute Firestore operations
2. Query the users collection to see onboarding status
3. Update user documents to simulate onboarding progress
4. Verify the onboarding completion flow

Example MCP commands:

// Create test user
firestore_create_document('users/test-onboarding-user', {...userData})

// Query incomplete onboarding
firestore_query_collection('users', [
  {'field': 'onboardingCompleted', 'op': 'EQUAL', 'compare_value': {'boolean_value': 'false'}}
])

// Update onboarding step
firestore_update_document('users/test-onboarding-user', {
  'onboardingSteps.personalInfo': {...stepData}
})

// Complete onboarding
firestore_update_document('users/test-onboarding-user', {
  'onboardingCompleted': true,
  'onboardingCompletedAt': FieldValue.serverTimestamp()
})
*/
