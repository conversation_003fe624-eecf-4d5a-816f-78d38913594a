// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:openfit/app/app.dart';

void main() {
  testWidgets('OpenFit app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const OpenFitApp());

    // Verify that our app loads with the dashboard
    expect(find.text('OpenFit'), findsOneWidget);
    expect(find.text('Welcome back, User!'), findsOneWidget);
    expect(find.text('Dashboard'), findsOneWidget);

    // Verify navigation bar is present
    expect(find.text('Workouts'), findsOneWidget);
    expect(find.text('Nutrition'), findsOneWidget);
    expect(find.text('Profile'), findsOneWidget);
  });
}
